{"models": [{"title": "GPT-4o (Local)", "provider": "openai", "model": "gpt-4o", "apiKey": "YOUR_OPENAI_API_KEY_HERE", "contextLength": 128000, "systemMessage": "You are an expert software developer and AI agent. You can analyze code, make changes, and help with complex development tasks.", "completionOptions": {"temperature": 0.3, "maxTokens": 4000}}, {"title": "Claude 3.5 Sonnet (Local)", "provider": "anthropic", "model": "claude-3-5-sonnet-20241022", "apiKey": "YOUR_ANTHROPIC_API_KEY_HERE", "contextLength": 200000, "systemMessage": "You are an expert software developer and AI agent. You can analyze code, make changes, and help with complex development tasks.", "completionOptions": {"temperature": 0.3, "maxTokens": 4000}}, {"title": "GPT-3.5 Turbo (Fast)", "provider": "openai", "model": "gpt-3.5-turbo", "apiKey": "YOUR_OPENAI_API_KEY_HERE", "contextLength": 16000, "systemMessage": "You are a helpful coding assistant.", "completionOptions": {"temperature": 0.2, "maxTokens": 2000}}], "tabAutocompleteModel": {"title": "Codestral Autocomplete", "provider": "mistral", "model": "codestral-latest", "apiKey": "YOUR_MISTRAL_API_KEY_HERE", "completionOptions": {"temperature": 0.1, "maxTokens": 500}}, "embeddingsProvider": {"provider": "openai", "model": "text-embedding-3-small", "apiKey": "YOUR_OPENAI_API_KEY_HERE"}, "reranker": {"name": "voyage", "params": {"apiKey": "YOUR_VOYAGE_API_KEY_HERE"}}, "customCommands": [{"name": "test", "prompt": "{{{ input }}}\n\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.", "description": "Write unit tests for highlighted code"}], "contextProviders": [{"name": "currentFile", "params": {}}, {"name": "diff", "params": {}}, {"name": "terminal", "params": {}}, {"name": "problems", "params": {}}, {"name": "folder", "params": {}}, {"name": "codebase", "params": {}}, {"name": "url", "params": {}}], "slashCommands": [{"name": "edit", "description": "Edit selected code"}, {"name": "comment", "description": "Write comments for the selected code"}, {"name": "share", "description": "Export the current chat session to markdown"}, {"name": "cmd", "description": "Generate a shell command"}, {"name": "commit", "description": "Generate a git commit message"}], "disableIndexing": false, "disableSessionTitles": false, "allowAnonymousTelemetry": false, "experimental": {"useTools": true, "modelRoles": {"inlineEdit": "GPT-4o (Local)", "applyCodeBlock": "Claude 3.5 Sonnet (Local)"}}, "tools": [{"type": "function", "function": {"name": "edit_file", "description": "Edit a file by replacing specific content", "parameters": {"type": "object", "properties": {"filepath": {"type": "string", "description": "Path to the file to edit"}, "old_content": {"type": "string", "description": "Content to replace"}, "new_content": {"type": "string", "description": "New content to insert"}}, "required": ["filepath", "old_content", "new_content"]}}, "displayTitle": "Edit File", "wouldLikeTo": "edit a file", "readonly": false}, {"type": "function", "function": {"name": "create_file", "description": "Create a new file with specified content", "parameters": {"type": "object", "properties": {"filepath": {"type": "string", "description": "Path where the new file should be created"}, "content": {"type": "string", "description": "Content for the new file"}}, "required": ["filepath", "content"]}}, "displayTitle": "Create File", "wouldLikeTo": "create a new file", "readonly": false}, {"type": "function", "function": {"name": "run_command", "description": "Execute a shell command", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "Shell command to execute"}}, "required": ["command"]}}, "displayTitle": "Run Command", "wouldLikeTo": "execute a command", "readonly": false}]}