{
  "models": [
    {
      "title": "GPT-4o Monica",
      "provider": "openai",
      "model": "gpt-4o",
      "systemMessage": "Eres un asistente de desarrollo experto especializado en análisis y mejora de código. Tu objetivo es proporcionar sugerencias precisas y prácticas que mejoren la calidad, eficiencia y legibilidad del código."
    },
    {
      "title": "Claude 3.7 Sonnet Latest",
      "provider": "anthropic",
      "model": "claude-3-7-sonnet-latest",
      "systemMessage": "<PERSON><PERSON>, un asistente de programación experto desarrollado por Anthropic. Especializas en análisis profundo de código, explicaciones detalladas y generación de soluciones elegantes."
    },
    {
      "title": "Claude 3.7 Sonnet Thinking",
      "provider": "anthropic",
      "model": "claude-3-7-sonnet-latest-thinking",
      "systemMessage": "Eres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones."
    },
    {
      "title": "Claude 3.7 Sonnet",
      "provider": "anthropic",
      "model": "claude-3-7-sonnet-latest",
      "version": "3.7.0",  // Track model version explicitly
      // Documentation: https://docs.anthropic.com/claude/reference/versions
      "systemMessage": "Eres un asistente de programación experto. Tu especialidad es entender rápidamente problemas complejos y ofrecer soluciones concretas y prácticas."
    }
  ],
  "tabAutocompleteModel": {
    "title": "Fast Autocomplete",
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "temperature": 0.1,
    "maxTokens": 100
  },
  "embeddingsProvider": {
    "provider": "free-trial"
  },
  "reranker": {
    "name": "free-trial"
  },
  "rules": [
    "Always analyze multiple related files when making changes to understand the full context",
    "Automatically create documentation (README.md, CHANGELOG.md) when creating new projects or major changes",
    "When editing code, always check for related files (tests, configs, docs) that might need updates",
    "Create session summaries in markdown format after complex multi-file operations",
    "Use semantic search across the codebase before making assumptions about existing functionality",
    "Always validate configuration changes by checking related files and dependencies",
    "When creating new files, follow the existing project structure and naming conventions",
    "Proactively suggest improvements and optimizations when analyzing code",
    "Create comprehensive documentation for any new features or significant changes",
    "Always consider the impact of changes on the entire project ecosystem",
    "Use tools autonomously to implement changes across multiple files when beneficial",
    "Maintain consistency in code style and patterns across the entire project"
  ],
  "customCommands": [
    {
      "name": "test",
      "prompt": "{{{ input }}}\n\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.",
      "description": "Write unit tests for highlighted code"
    },
    {
      "name": "refactor",
      "prompt": "{{{ input }}}\n\nRefactor the selected code to improve its readability, efficiency, and maintainability without changing its functionality. Focus on applying best practices for the language and eliminating any code smells or potential bugs.",
      "description": "Refactor highlighted code"
    },
    {
      "name": "docs",
      "prompt": "{{{ input }}}\n\nGenerate comprehensive documentation for the selected code, including function descriptions, parameter explanations, return values, and usage examples.",
      "description": "Document highlighted code"
    },
    {
      "name": "analyze-project",
      "prompt": "Perform a comprehensive analysis of the entire project:\n1. Examine all configuration files\n2. Analyze code structure and patterns\n3. Identify potential improvements\n4. Check for missing documentation\n5. Create a detailed analysis report\n\nUse tools to read multiple files and create a comprehensive analysis document.",
      "description": "Deep project analysis with multi-file examination"
    },
    {
      "name": "session-summary",
      "prompt": "Create a detailed summary of this development session:\n1. Changes made to files\n2. New features implemented\n3. Issues resolved\n4. Next steps and recommendations\n5. Files that were created or modified\n\nSave this as a markdown file with timestamp in the filename using tools.",
      "description": "Generate session summary documentation"
    },
    {
      "name": "auto-improve",
      "prompt": "{{{ input }}}\n\nAutonomously improve the selected code or project area:\n1. Analyze the code and related files\n2. Identify improvement opportunities\n3. Implement optimizations using tools\n4. Update related documentation\n5. Create tests if needed\n\nWork autonomously across multiple files to implement comprehensive improvements.",
      "description": "Autonomous code improvement with multi-file changes"
    },
    {
      "name": "read-large-file",
      "prompt": "{{{ input }}}\n\nRead and analyze a large file using chunking strategy:\n1. Use read_file_chunks tool to read the file in manageable pieces\n2. Analyze each chunk for patterns, structure, and content\n3. Provide a comprehensive summary of the entire file\n4. Identify key sections, functions, classes, or components\n5. Suggest improvements or optimizations\n\nHandle files that exceed normal reading limits by processing them intelligently in chunks.",
      "description": "Read and analyze large files using chunking"
    },
    {
      "name": "analyze-codebase",
      "prompt": "Perform a comprehensive analysis of the entire codebase:\n1. Use analyze_large_codebase tool to examine all files\n2. Process large files in chunks to avoid limits\n3. Identify architecture patterns and structure\n4. Find dependencies and relationships between files\n5. Create a detailed codebase analysis report\n6. Suggest architectural improvements\n\nWork with large codebases by intelligently chunking and processing files.",
      "description": "Comprehensive codebase analysis with chunking support"
    }
  ],
  "contextProviders": [
    {
      "name": "currentFile",
      "params": {}
    },
    {
      "name": "diff",
      "params": {}
    },
    {
      "name": "terminal",
      "params": {}
    },
    {
      "name": "problems",
      "params": {}
    },
    {
      "name": "folder",
      "params": {}
    },
    {
      "name": "codebase",
      "params": {}
    },
    {
      "name": "url",
      "params": {}
    }
  ],
  "slashCommands": [
    {
      "name": "edit",
      "description": "Edit selected code"
    },
    {
      "name": "comment",
      "description": "Write comments for the selected code"
    },
    {
      "name": "share",
      "description": "Export the current chat session to markdown"
    },
    {
      "name": "cmd",
      "description": "Generate a shell command"
    },
    {
      "name": "commit",
      "description": "Generate a git commit message"
    }
  ],
  "disableIndexing": false,
  "disableSessionTitles": false,
  "allowAnonymousTelemetry": false,
  "indexingConfig": {
    "maxFileSize": 2000000,
    "includePatterns": ["**/*.ts", "**/*.js", "**/*.py", "**/*.md", "**/*.json", "**/*.yaml", "**/*.yml", "**/*.tsx", "**/*.jsx"],
    "excludePatterns": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/.git/**", "**/coverage/**", "**/.next/**"],
    "enableSemanticSearch": true,
    "chunkSize": 2000,
    "chunkOverlap": 300,
    "smartChunking": true,
    "chunkStrategy": "semantic",
    "preserveCodeBlocks": true,
    "maxChunksPerFile": 50,
    "minChunkSize": 500
  },
  "experimental": {
    "useTools": true,
    "autonomousMode": true,
    "multiFileAnalysis": true,
    "autoDocumentation": true,
    "sessionTracking": true,
    "modelRoles": {
      "inlineEdit": "GPT-4o Monica",
      "applyCodeBlock": "Claude 3.7 Sonnet Thinking",
      "analysis": "Claude 3.7 Sonnet Latest",
      "documentation": "Claude 3.5 Sonnet"
    }
  },
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "edit_file",
        "description": "Edit a file by replacing specific content",
        "parameters": {
          "type": "object",
          "properties": {
            "filepath": {
              "type": "string",
              "description": "Path to the file to edit"
            },
            "old_content": {
              "type": "string",
              "description": "Content to replace"
            },
            "new_content": {
              "type": "string",
              "description": "New content to insert"
            }
          },
          "required": ["filepath", "old_content", "new_content"]
        }
      },
      "displayTitle": "Edit File",
      "wouldLikeTo": "edit a file",
      "readonly": false
    },
    {
      "type": "function",
      "function": {
        "name": "create_file",
        "description": "Create a new file with specified content",
        "parameters": {
          "type": "object",
          "properties": {
            "filepath": {
              "type": "string",
              "description": "Path where the new file should be created"
            },
            "content": {
              "type": "string",
              "description": "Content for the new file"
            }
          },
          "required": ["filepath", "content"]
        }
      },
      "displayTitle": "Create File",
      "wouldLikeTo": "create a new file",
      "readonly": false
    },
    {
      "type": "function",
      "function": {
        "name": "run_command",
        "description": "Execute a shell command",
        "parameters": {
          "type": "object",
          "properties": {
            "command": {
              "type": "string",
              "description": "Shell command to execute"
            }
          },
          "required": ["command"]
        }
      },
      "displayTitle": "Run Command",
      "wouldLikeTo": "execute a command",
      "readonly": false
    },
    {
      "type": "function",
      "function": {
        "name": "read_multiple_files",
        "description": "Read and analyze multiple files at once",
        "parameters": {
          "type": "object",
          "properties": {
            "filepaths": {
              "type": "array",
              "items": {"type": "string"},
              "description": "Array of file paths to read"
            },
            "analysis_type": {
              "type": "string",
              "description": "Type of analysis to perform (structure, dependencies, patterns, etc.)"
            }
          },
          "required": ["filepaths"]
        }
      },
      "displayTitle": "Read Multiple Files",
      "wouldLikeTo": "analyze multiple files",
      "readonly": true
    },
    {
      "type": "function",
      "function": {
        "name": "create_session_summary",
        "description": "Create a comprehensive session summary with timestamp",
        "parameters": {
          "type": "object",
          "properties": {
            "session_type": {
              "type": "string",
              "description": "Type of session (development, analysis, refactoring, etc.)"
            },
            "changes_made": {
              "type": "array",
              "items": {"type": "string"},
              "description": "List of changes made during the session"
            },
            "files_affected": {
              "type": "array",
              "items": {"type": "string"},
              "description": "List of files that were created or modified"
            }
          },
          "required": ["session_type"]
        }
      },
      "displayTitle": "Create Session Summary",
      "wouldLikeTo": "document the session",
      "readonly": false
    },
    {
      "type": "function",
      "function": {
        "name": "read_file_chunks",
        "description": "Read large files in manageable chunks with overlap",
        "parameters": {
          "type": "object",
          "properties": {
            "filepath": {
              "type": "string",
              "description": "Path to the file to read in chunks"
            },
            "chunk_size": {
              "type": "number",
              "description": "Size of each chunk in characters (default: 2000)",
              "default": 2000
            },
            "overlap": {
              "type": "number",
              "description": "Overlap between chunks in characters (default: 300)",
              "default": 300
            },
            "start_chunk": {
              "type": "number",
              "description": "Starting chunk number (default: 0)",
              "default": 0
            },
            "max_chunks": {
              "type": "number",
              "description": "Maximum number of chunks to read (default: 10)",
              "default": 10
            }
          },
          "required": ["filepath"]
        }
      },
      "displayTitle": "Read File in Chunks",
      "wouldLikeTo": "read a large file in chunks",
      "readonly": true
    },
    {
      "type": "function",
      "function": {
        "name": "analyze_large_codebase",
        "description": "Analyze large codebase by reading files in chunks and summarizing",
        "parameters": {
          "type": "object",
          "properties": {
            "directory": {
              "type": "string",
              "description": "Directory to analyze"
            },
            "file_patterns": {
              "type": "array",
              "items": {"type": "string"},
              "description": "File patterns to include (e.g., ['*.ts', '*.js', '*.py'])"
            },
            "max_file_size": {
              "type": "number",
              "description": "Maximum file size to process in bytes (default: 100000)",
              "default": 100000
            },
            "chunk_strategy": {
              "type": "string",
              "enum": ["function", "class", "lines", "semantic"],
              "description": "Strategy for chunking files",
              "default": "semantic"
            }
          },
          "required": ["directory"]
        }
      },
      "displayTitle": "Analyze Large Codebase",
      "wouldLikeTo": "analyze a large codebase intelligently",
      "readonly": true
    }
  ]
}