{"models": [{"title": "Claude 3.5 Sonnet (Free Trial)", "provider": "free-trial", "model": "claude-3-5-sonnet-latest", "systemMessage": "You are an expert software developer. You give helpful and concise responses."}, {"title": "GPT-4o (Free Trial)", "provider": "free-trial", "model": "gpt-4o", "systemMessage": "You are an expert software developer. You give helpful and concise responses."}, {"title": "Llama3.1 70b (Free Trial)", "provider": "free-trial", "model": "llama3.1-70b", "systemMessage": "You are an expert software developer. You give helpful and concise responses."}, {"title": "Codestral (Free Trial)", "provider": "free-trial", "model": "codestral-latest", "systemMessage": "You are an expert software developer. You give helpful and concise responses."}, {"model": "claude-3-5-sonnet-latest", "provider": "anthropic", "apiKey": "", "title": "Claude 3.5 Sonnet"}, {"model": "claude-3-5-sonnet-latest", "provider": "anthropic", "apiKey": "", "title": "Claude 3.5 Sonnet"}, {"title": "Grok Beta", "model": "grok-beta", "contextLength": 128000, "apiKey": "************************************************************************************", "provider": "xAI"}, {"apiKey": "XfeFh79plKmcdMWwx2kUVBDTMV9Og7ey", "title": "Codestral (1)", "model": "codestral-latest", "provider": "mistral"}], "tabAutocompleteModel": {"title": "Tab Autocomplete", "provider": "free-trial", "model": "codestral-latest"}, "customCommands": [{"name": "test", "prompt": "{{{ input }}}\n\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.", "description": "Write unit tests for highlighted code"}], "contextProviders": [{"name": "code", "params": {}}, {"name": "docs", "params": {}}, {"name": "diff", "params": {}}, {"name": "terminal", "params": {}}, {"name": "problems", "params": {}}, {"name": "folder", "params": {}}, {"name": "codebase", "params": {}}], "slashCommands": [{"name": "share", "description": "Export the current chat session to markdown"}, {"name": "cmd", "description": "Generate a shell command"}, {"name": "commit", "description": "Generate a git commit message"}], "embeddingsProvider": {"provider": "free-trial"}, "reranker": {"name": "free-trial"}}