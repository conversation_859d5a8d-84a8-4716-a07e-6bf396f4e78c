# Características Principales de Continue.dev

**Continue** es una herramienta que permite a los desarrolladores crear, compartir y utilizar asistentes de código de IA personalizados. Se integra con entornos de desarrollo como VS Code y JetBrains a través de extensiones, y ofrece un centro de modelos, reglas, prompts, documentación y otros bloques de construcción.

## Características Destacadas:

*   **Agent**: Permite realizar cambios sustanciales en la base de código de manera más eficiente.
*   **Chat**: Facilita la interacción con un LLM (Large Language Model) directamente desde el IDE para obtener ayuda y asistencia.
*   **Autocomplete**: Proporciona sugerencias de código en línea mientras se escribe, mejorando la velocidad y precisión de la codificación.
*   **Edit**: Ofrece una forma conveniente de modificar el código sin necesidad de salir del archivo actual.

## Primeros Pasos:

Para comenzar a usar Continue, se puede consultar la documentación de instalación en [continue.dev/docs/getting-started/install](https://continue.dev/docs/getting-started/install).

## Contribución:

Los interesados en contribuir pueden leer la [guía de contribución](https://github.com/continuedev/continue/blob/main/CONTRIBUTING.md) y unirse al canal `#contribute` en Discord.

## Licencia:

El proyecto está bajo la licencia [Apache 2.0 © 2023-2024 Continue Dev, Inc.](./LICENSE).