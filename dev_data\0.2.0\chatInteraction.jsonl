{"timestamp":"2025-06-03T23:52:59.340Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in agent mode.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n","completion":"","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:53:03.932Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in agent mode.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nbuiltin_read_file({\"filepath\": \"src/main.py\"})\n\n<tool>\nbuiltin_read_file failed with the message: Could not find file src/main.py\n\nPlease try something else or request further instructions.\n\n","completion":"","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:53:08.097Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in agent mode.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nbuiltin_read_file({\"filepath\": \"src/main.py\"})\n\n<tool>\nbuiltin_read_file failed with the message: Could not find file src/main.py\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/\", \"recursive\": false})\n\n<tool>\n.continuerc.json\n.diffs/\n.migrations/\n.utils/\nconfig.ts\ndev_data/\nindex/\npackage.json\nsessions/\ntsconfig.json\ntypes/\n\n","completion":"","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:53:12.907Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in agent mode.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nbuiltin_read_file({\"filepath\": \"src/main.py\"})\n\n<tool>\nbuiltin_read_file failed with the message: Could not find file src/main.py\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/\", \"recursive\": false})\n\n<tool>\n.continuerc.json\n.diffs/\n.migrations/\n.utils/\nconfig.ts\ndev_data/\nindex/\npackage.json\nsessions/\ntsconfig.json\ntypes/\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/src\", \"recursive\": false})\n\n<tool>\nbuiltin_ls failed with the message: Directory /src not found. Make sure to use forward-slash paths. Do not use e.g. \".\"\n\nPlease try something else or request further instructions.\n\n","completion":"","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:53:15.437Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in agent mode.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nbuiltin_read_file({\"filepath\": \"src/main.py\"})\n\n<tool>\nbuiltin_read_file failed with the message: Could not find file src/main.py\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/\", \"recursive\": false})\n\n<tool>\n.continuerc.json\n.diffs/\n.migrations/\n.utils/\nconfig.ts\ndev_data/\nindex/\npackage.json\nsessions/\ntsconfig.json\ntypes/\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/src\", \"recursive\": false})\n\n<tool>\nbuiltin_ls failed with the message: Directory /src not found. Make sure to use forward-slash paths. Do not use e.g. \".\"\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/src\", \"recursive\": false})\n\n<tool>\nbuiltin_ls failed with the message: Directory /src not found. Make sure to use forward-slash paths. Do not use e.g. \".\"\n\nPlease try something else or request further instructions.\n\n","completion":"","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:53:17.918Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in agent mode.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nbuiltin_read_file({\"filepath\": \"src/main.py\"})\n\n<tool>\nbuiltin_read_file failed with the message: Could not find file src/main.py\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/\", \"recursive\": false})\n\n<tool>\n.continuerc.json\n.diffs/\n.migrations/\n.utils/\nconfig.ts\ndev_data/\nindex/\npackage.json\nsessions/\ntsconfig.json\ntypes/\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/src\", \"recursive\": false})\n\n<tool>\nbuiltin_ls failed with the message: Directory /src not found. Make sure to use forward-slash paths. Do not use e.g. \".\"\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/src\", \"recursive\": false})\n\n<tool>\nbuiltin_ls failed with the message: Directory /src not found. Make sure to use forward-slash paths. Do not use e.g. \".\"\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/\", \"recursive\": true})\n\n<tool>\n.continuerc.json\n.diffs/\n.migrations/\n.utils/\nconfig.ts\ndev_data/\nindex/\npackage.json\nsessions/\ntsconfig.json\ntypes/\ntypes/core/\ntypes/core/index.d.ts\nsessions/5c85b314-e75f-4451-bc6e-9a967395f6fb.json\nsessions/d5acb603-f93d-46e2-850f-7e6c91cf5d06.json\nsessions/fa34012f-9ced-47ef-92f1-722514243e12.json\nsessions/sessions.json\nindex/globalContext.json\nindex/index.sqlite-shm\nindex/index.sqlite-wal\nindex/lancedb/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_latest.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/1.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/2.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/3.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/4.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/5.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/0-5af4359f-7da5-4a02-9ea4-238b8d2b907d.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/1-d9e4625f-ded4-4a92-94f4-a7a1d814579f.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/2-7fc3fd72-12ac-4261-ad30-3b891b763fb3.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/3-01a64777-54cd-4f4d-bd66-e06497af98c5.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/4-b421347f-7db6-4680-ab6f-de75805468f8.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/39f521ad-5fdf-4a7f-bc6c-e40dcd89a297.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/7f59e86e-62a8-4507-a840-08aaf73ddf61.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/9a366a0f-e1ee-4de4-bda2-589500b57613.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/ecd6f469-9077-4d09-a542-bcfb1a3a86dc.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/f4d02306-4791-4cf1-bab9-f19f23e440d9.lance\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/data/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_latest.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/1.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/2.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/3.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/0-24c4c43e-cb93-4e35-82aa-b057b5a5459c.txn\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/1-f8a393a3-cc86-4a5d-b038-558a3db23d80.txn\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/2-2639ce1b-feb8-4060-a151-d2641fc5c078.txn\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/data/121a9d98-e605-40fe-8eed-25f469dc8f09.lance\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/data/81af9201-cc26-4e47-8b9b-6193a9738f21.lance\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_latest.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/1.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/2.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/3.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/0-28c91883-f2c2-4315-9663-2655917487d1.txn\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/1-0fb0f504-a628-443c-92bf-66c13f9f097c.txn\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/2-905dff5d-819e-4571-9ca1-465fd84ccd11.txn\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/1a7760db-e2c6-4dbb-84be-98d8de90e931.lance\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/5a1668fa-2f4b-4023-b2a8-2279404cb8a4.lance\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/f7f2d41b-37e0-4677-a12a-d1e3b6bd7a25.lance\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-3d580e51-7483-4160-bf44-ce005cd26f33.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-9d24651c-9163-4870-ad1b-051af848c359.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-eba77257-aeed-4e49-86b6-364959eff341.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-934e3358-87eb-4efa-b3b1-72c3b09f6894.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-89fd1114-2c5f-474e-bfed-365c73b187fc.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-4-7145968909395871742.arrow\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/3f12a079-82df-41db-831a-13111d61b135.lance\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/aae6d80e-eca5-46ce-a2a2-66ce57c94911.lance\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/cf95b38f-44b1-46e0-b48c-d25a00fe07ed.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_transactions/0-b6db3bbc-6fc5-4dab-a8d3-c7ed4d43e99c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/data/05375ef0-8f10-4a06-af68-64af1945e3ed.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/10.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/100.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/101.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/102.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/103.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/104.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/105.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/106.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/11.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/12.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/13.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/14.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/15.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/16.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/17.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/18.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/19.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/20.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/21.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/22.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/23.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/24.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/25.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/26.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/27.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/28.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/29.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/30.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/31.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/32.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/33.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/34.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/35.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/36.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/37.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/38.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/39.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/40.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/41.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/42.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/43.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/44.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/45.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/46.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/47.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/48.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/49.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/50.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/51.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/52.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/53.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/54.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/55.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/56.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/57.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/58.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/59.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/6.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/60.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/61.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/62.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/63.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/64.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/65.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/66.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/67.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/68.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/69.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/7.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/70.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/71.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/72.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/73.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/74.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/75.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/76.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/77.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/78.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/79.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/8.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/80.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/81.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/82.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/83.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/84.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/85.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/86.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/87.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/88.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/89.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/9.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/90.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/91.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/92.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/93.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/94.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/95.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/96.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/97.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/98.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/99.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-468e77d5-db40-4271-8af9-bbaa1eb30fe3.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-ed42dc95-4d3c-41af-b1cf-aabb388eaa29.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/10-74888248-52ae-43f6-8c8c-619119b9f7e6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/100-6e7bddd2-02aa-451f-b655-424ad7410d87.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/101-b2015097-7fd1-498b-a532-780e5618cc9c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/102-536557e3-3869-46f8-a216-f97864c903cb.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/103-4151fc16-3df4-4471-b811-f71307d1dec2.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/104-cc7a7ab5-96c3-43e7-a6ea-fd34e63eec8b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/105-08a8f106-dd7b-4cab-a76e-50470bedaa72.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/11-c6213032-117b-477d-8bf0-ddbbc6583044.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/12-91071180-b423-4373-bb71-677a336c70ae.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/13-8aeb0d60-61e0-43ed-a5ab-be440b0db8a3.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/14-469299f3-41bf-4e52-93b9-5f862f3a612a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/15-f228b455-d29b-432b-a72e-ac8ff136c552.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/16-74c0b079-5f72-4da6-99ef-120722c8a0e9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/17-a7c5cfb6-7bc7-4c64-a49a-1e1c809a5d1d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/18-9229d869-6c69-47e4-bae6-af4c712494b7.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/19-876d9abb-ed84-4229-9a9b-b7b1aca375dc.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-f83dae76-79ec-4ca8-b943-6da9bdee3c74.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/20-fff8751a-ecc3-4397-9e93-3c365e24bc97.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/21-942a0c35-da16-4635-a1d6-ba054ca03185.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/22-a3b7e027-6ac4-4979-ae03-4b265cba00da.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/23-9bdee7db-901f-492e-a176-66d16547465b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/24-3933db4c-42b5-48ee-93cb-145cb648b5be.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/25-894a625c-b19f-4f2b-8246-345ad1d65ccd.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/26-a86c2d51-5bc5-4f0c-8940-952094ac8f36.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/27-87aae4bf-68a5-43b9-843a-f5611c71aec6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/28-faf4a5fa-846f-41ee-b111-1807cfac42b6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/29-eb20ca53-cffd-4b92-b858-8ddd802c902b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-7ff31105-7c00-4f33-877d-0291e3e22f00.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/30-b6803304-2850-48e6-9d5b-c1f393a32c03.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/31-766bc588-1f9b-4df7-addb-5118f3844978.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/32-5e87077b-a8fd-4ef9-8343-8301b4afb1db.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/33-7693d89b-57a2-4521-8bc5-a76c63feb939.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/34-035230a9-f57f-4c06-8bac-43f2bfc6f97e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/35-6e8df5f5-36f0-445b-b8c9-3e80e00c408f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/36-73fb5481-bbc3-4671-819e-9394a9a6a0b1.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/37-8547c318-9d4e-44d9-b7f5-34f8ab2c8e07.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/38-613ced78-c5a6-4dda-a56a-f19bcfbaa6dd.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/39-916e4b2c-4bcb-40ae-910f-bd03f15fe845.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-504bb725-2a44-42e3-9ab2-f3f63bcd901e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/40-dacdeba6-c7db-4761-a908-c68c7e058410.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/41-39682dfd-f578-46a1-88dc-ed1bd010dd32.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/42-4d88d8a2-1d37-4a5f-9841-1bce9c5e446c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/43-26f91edd-59c5-4981-bb06-079c4c58fd5b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/44-8b384d44-19c2-41be-9744-c9310e9e476d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/45-b1ffcfcd-6d5c-477a-b1fe-b54ad03ca9d9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/46-57b39e68-980b-4a6b-bb9e-4099babfdc01.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/47-8e162208-df3b-43c1-bcfd-965695c7477f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/48-ca711e95-145a-4038-83be-5f9764932b40.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/49-13e2d25b-a872-4d15-9376-c223aa107973.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/5-f41fdd28-57fc-4831-bc21-2653f0f92871.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/50-8b1993bc-d13e-4480-9342-6cd842a5a5ca.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/51-ead36124-ace5-41a3-a545-0c0582938a26.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/52-3bbb0244-e16d-40d5-a971-538f085b337e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/53-c0caa574-1625-4281-9e32-6b402fc59d84.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/54-f1f5036b-5a82-481b-9345-d234f9699d40.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/55-6f2223ab-4e16-47e8-9f4d-fee7ba1ade4d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/56-e9684650-1c8c-480d-816b-4f750d831396.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/57-5d46798a-4e3f-4940-a68f-dd75d03ad7ee.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/58-c4d490eb-ff27-4226-b1cf-c281bf8980c9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/59-97e949c7-b0b8-4a64-bd86-96c398172b0a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/6-f8800850-1e24-47e5-ac20-9f2634718a19.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/60-c9c59135-b389-4720-91e2-a5d0c2894af4.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/61-5ea97e54-8188-409c-884d-4cdd8098eee5.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/62-c6c5b811-9ac5-4068-aa53-9491d4426acd.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/63-97dc12f4-db41-4304-83a6-8eb0f2cca71c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/64-34f1922b-2f5a-4581-8186-6e011a6e08a9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/65-91acbe83-d1c6-4d64-bb23-1eac20e7b373.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/66-da2c7542-5439-407f-80f7-a03528ec087a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/67-e2bcedda-c029-4f56-a670-14457d59e179.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/68-d9e0b166-d8c3-42eb-a7b6-76ad3ff7b991.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/69-25095690-b542-4d2d-8ad9-5fea3f3e3374.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/7-68f9cd1e-d503-474c-983e-5040c628e095.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/70-dc412984-04d7-4358-867f-4e6510935697.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/71-44bdc649-e77b-42a5-9187-a814dc583a11.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/72-c67f07be-6e8f-4c51-98ed-ef534b407c91.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/73-4d76e217-0570-4252-99e0-371a1ab3bf16.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/74-27505600-6449-4cf6-8703-719c219dfbab.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/75-5623f748-cbea-4dee-b64b-6337c0abf864.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/76-52f77e1f-0b67-4cdf-a5aa-6af4b9e59924.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/77-1f313740-eb31-4bff-ab7e-3c0f5c599126.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/78-024beb90-b2b3-45c5-a8a8-94eebcd08d07.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/79-6302504e-a832-4c29-9fed-1014fb447e64.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/8-75900a7d-24ae-491f-bac4-0f16b3bd9032.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/80-0037533b-3d77-4834-af24-6afb481341a0.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/81-409b94ee-73e7-4d2f-befa-e8cc737a3931.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/82-fcfc0956-5cb3-4c9c-bec9-4b99e33f5192.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/83-7443c936-7d28-486e-915c-5e620634a854.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/84-61f8358f-1367-49db-b11d-b6dec046d56f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/85-8a9dfcb7-3be3-4a83-9d57-c03487cc7162.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/86-a2bf971c-ef86-4789-8111-efd139e50c8e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/87-41a32a17-89ee-4906-8548-3309c7b2fe49.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/88-a7e747c9-0f4d-404c-99ed-64d2c9b72d5f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/89-dc5034e8-78e9-475b-9fef-0be64e60a67d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/9-33a4a90b-ff99-4044-a47c-415a2d954bde.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/90-ee1904d9-e3f4-402e-aa39-9a9a5ca583cc.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/91-ea9b6e0f-7776-4f54-ac88-930e68091df9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/92-682fde34-cadc-4172-9541-521829b9c36b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/93-1fdbc2ae-f23c-4c8a-af5b-e06d5b055bd0.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/94-4ca5497f-b44b-4a13-a352-99555bb3c3ec.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/95-e6b68c6a-0ec8-4ce4-a1ec-50c75c35d832.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/96-8ec568c8-d004-48c8-9bc6-1631069ba2d6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/97-c9b87012-f0e5-4bb1-916d-bdff158added.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/98-f2379db7-89df-4ad7-ae15-eb380faff829.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/99-08d2bd59-0373-4db7-a01c-658bb37bda9a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-10-549925515030042835.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-11-12312290082990278072.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-12-12546863215572302023.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-13-8607086708791890548.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-14-7282690490916650355.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-15-13222122184769230605.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-69-5162146069800354050.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-7-438018867623872040.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-70-2700198863766637416.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-73-16813622875355296403.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-76-16458288028775487944.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-8-2681837767972520945.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-9-10246354014714756532.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-102-6397084758809297613.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-61-17895828905791776150.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-64-14066344455790186079.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-67-6555237003325910429.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-68-15830087394093568438.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-75-13561457464375439557.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-96-4459926547751262314.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/3-66-14500712657653123367.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-22-14071483736087719395.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-23-12613990763145386882.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-24-12814949837301935385.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-25-17329030441548199866.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-28-9128495002814127692.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-30-11131948704441124328.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-31-15645374567477436001.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-32-14803077281707590285.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-33-10661437038108867062.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-34-9658603270659196864.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-35-14961367826904637998.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-38-17219353825538434573.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-39-10775335505413791967.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-40-3787116558452784214.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-41-13719717179808141218.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-42-3209286349079787144.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-43-13787602717360574820.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-44-6559160351853226391.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-45-8806561158590654924.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-50-5270997752933395436.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-51-10426699983941174985.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-52-18314369573543888363.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-53-13824656193748262753.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-54-6588177187933090424.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-55-4336790527423617822.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-56-5493389883289445430.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/8-101-10408064738517512984.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/8-97-5780112088545781433.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/000fd065-17c8-42f8-9ca9-bc83855d9afa.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/0b42d100-13d1-4f47-ae79-08772dda496d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/124330b4-6838-433f-8a7c-4c1e22f7867f.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/2225f1c7-cb6d-40c7-a0e3-61827aacafd3.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/2eabb10f-576c-4f7d-a9da-d7fb19cc8be3.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/3129b869-7f8a-4bd2-914a-6f918d73b62d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/4f471def-79b4-4578-9712-b68c9fadf64d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/5f41354f-2ec0-4b18-94f1-93c629bd9398.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/625daaa7-de0f-4d48-8f38-fec360d0bf70.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/680d8198-6896-470f-866e-869fa4112a43.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/68a8ce32-58e0-46b0-940b-8ce6ec403ce8.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/6a09ffea-7af0-43dc-bc27-9b5e5dbab190.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/70ee8ae4-df5d-42e8-8894-f1623849f9e1.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/77d35ded-96ed-462a-89eb-3430c06f652e.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/78e2e7af-9aea-4c6d-b8d7-0e781352b26d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/7cfd0d58-2891-4083-90aa-59528e03f0da.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/7d390a49-3449-49d1-8439-ade3ec1fe5d0.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/817d8ae5-b343-44ba-9b5c-3fabf7dbb19c.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/879f0778-4f37-4525-bc28-84c4f32e02c5.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/8e416056-04cb-49c4-9445-fff8aff245b0.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/90bb5d76-4226-4d63-bac4-952e107375f3.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/92b44b83-1898-4fd2-8601-f1dc3ec55ce9.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/961445ff-2970-40ce-abe8-54b7d2e5e861.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/9c4a3fc9-9606-4a59-b6b8-db10d516c249.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/c537bf3d-5adb-45df-ac01-43aac72922fe.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/cd83ef8e-fa82-47bb-aac6-ad6557bde697.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/cea08ee9-3beb-4e07-97bb-4f2e19339132.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/dbea38dc-f9c7-4849-9152-aa1e1e0e9f6c.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/ef62888f-2799-498a-9c37-3cab12834d8a.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/ff235532-adb7-45d1-82d3-32cd4f9981c6.lance\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-8a60bb17-11e2-4ca3-bb0c-5e41e8070ceb.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-56fa1033-6fa1-4bdc-ba64-3f2adbcba1cb.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-3cd7901b-86db-475f-86fd-32e0c714bdc5.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-d0bab694-ed93-4f4b-8d6f-170fcecab7dc.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-c6bf5636-040c-4598-9269-3f6425b58684.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-3-17773003357296133812.arrow\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/3fb7fd30-4cce-45f9-a8b8-af059554ac7f.lance\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/9e046747-cd1c-4467-94f6-75223162f66c.lance\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/a85b50df-2ef6-41aa-859e-32489705f205.lance\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-de7cce48-cb3d-444f-9b32-dee3ad3eb6c1.txn\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/1740f3f9-e724-4192-814d-583d0f7495a6.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/6.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/7.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-ef1c6b3b-350f-4432-9375-3d76d092a404.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-3b5c1bb1-3cb6-4e98-ada1-f10d0214c399.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-dbc75959-dbb2-4e9c-8fc1-a2e20acc8823.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-83dc829f-6d24-43cf-a481-816a65cbc12f.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-63b9dd84-d57e-42ea-a31d-ccb5166a35ca.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/5-d9298404-8d92-4b65-a4b0-d33d5c206135.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/6-a17d2425-2c91-4973-a6ba-fb0afdc973c9.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-5-4654674215173510631.arrow\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/1fe0f465-ae0c-4c85-8407-68ec4c6d9174.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/430e8a57-82cc-43ea-b254-de039ccd494c.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/6de441e4-1e69-4b3c-978c-aba04b001400.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/8d940b7a-5eae-44ce-8f65-55b61b2fdb57.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/afaa4b83-e6ef-44b3-af58-e8d6415c74d2.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-8980a58a-e585-4ee0-be3c-4fdaf601739f.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/c981262a-aa8f-464f-b510-f8dd5cbb9e20.lance\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-87d8323c-4cd3-47bc-8ad9-c2220bd2c8dd.txn\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-6e0d8127-0b62-46f9-999b-e974ea2d0378.txn\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/a33ef148-3f84-405f-91cb-3aa8eae25ba8.lance\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-f3f06a5f-8199-4eca-bd7b-52c4205542c5.txn\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/68d6b933-699e-46af-8dcc-cc85a1311d3e.lance\ndev_data/0.1.0/\ndev_data/0.2.0/\n.migrations/add_signature_column\n.migrations/delete_duplicate_code_snippets\n.migrations/lancedb_sqlite_artifact_id_column\n.migrations/sqlite_delete_docs_with_no_embeddingsProviderId\n.migrations/sqlite_modify_docs_columns_and_copy_to_config\n\n","completion":"No se encontró el archivo src/main.py. ¿Hay algo más en lo que pueda ayudarte?","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:53:38.876Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in agent mode.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nbuiltin_read_file({\"filepath\": \"src/main.py\"})\n\n<tool>\nbuiltin_read_file failed with the message: Could not find file src/main.py\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/\", \"recursive\": false})\n\n<tool>\n.continuerc.json\n.diffs/\n.migrations/\n.utils/\nconfig.ts\ndev_data/\nindex/\npackage.json\nsessions/\ntsconfig.json\ntypes/\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/src\", \"recursive\": false})\n\n<tool>\nbuiltin_ls failed with the message: Directory /src not found. Make sure to use forward-slash paths. Do not use e.g. \".\"\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/src\", \"recursive\": false})\n\n<tool>\nbuiltin_ls failed with the message: Directory /src not found. Make sure to use forward-slash paths. Do not use e.g. \".\"\n\nPlease try something else or request further instructions.\n\n<assistant>\nbuiltin_ls({\"dirPath\": \"/\", \"recursive\": true})\n\n<tool>\n.continuerc.json\n.diffs/\n.migrations/\n.utils/\nconfig.ts\ndev_data/\nindex/\npackage.json\nsessions/\ntsconfig.json\ntypes/\ntypes/core/\ntypes/core/index.d.ts\nsessions/5c85b314-e75f-4451-bc6e-9a967395f6fb.json\nsessions/d5acb603-f93d-46e2-850f-7e6c91cf5d06.json\nsessions/fa34012f-9ced-47ef-92f1-722514243e12.json\nsessions/sessions.json\nindex/globalContext.json\nindex/index.sqlite-shm\nindex/index.sqlite-wal\nindex/lancedb/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_latest.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/1.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/2.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/3.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/4.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/5.manifest\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/0-5af4359f-7da5-4a02-9ea4-238b8d2b907d.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/1-d9e4625f-ded4-4a92-94f4-a7a1d814579f.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/2-7fc3fd72-12ac-4261-ad30-3b891b763fb3.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/3-01a64777-54cd-4f4d-bd66-e06497af98c5.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/4-b421347f-7db6-4680-ab6f-de75805468f8.txn\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/39f521ad-5fdf-4a7f-bc6c-e40dcd89a297.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/7f59e86e-62a8-4507-a840-08aaf73ddf61.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/9a366a0f-e1ee-4de4-bda2-589500b57613.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/ecd6f469-9077-4d09-a542-bcfb1a3a86dc.lance\nindex/lancedb/filec3AUsersMario20ESCDocumentsNONEvectordbFreeTrialUNSPECIFIED.lance/data/f4d02306-4791-4cf1-bab9-f19f23e440d9.lance\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/data/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_latest.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/1.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/2.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_versions/3.manifest\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/0-24c4c43e-cb93-4e35-82aa-b057b5a5459c.txn\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/1-f8a393a3-cc86-4a5d-b038-558a3db23d80.txn\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/_transactions/2-2639ce1b-feb8-4060-a151-d2641fc5c078.txn\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/data/121a9d98-e605-40fe-8eed-25f469dc8f09.lance\nindex/lancedb/filec3AUsersMario20ESC.monica-codeNONEvectordbFreeTrialUNSPECIFIED.lance/data/81af9201-cc26-4e47-8b9b-6193a9738f21.lance\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_latest.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/1.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/2.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_versions/3.manifest\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/0-28c91883-f2c2-4315-9663-2655917487d1.txn\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/1-0fb0f504-a628-443c-92bf-66c13f9f097c.txn\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/_transactions/2-905dff5d-819e-4571-9ca1-465fd84ccd11.txn\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/1a7760db-e2c6-4dbb-84be-98d8de90e931.lance\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/5a1668fa-2f4b-4023-b2a8-2279404cb8a4.lance\nindex/lancedb/filec3AMoldflowcontinuemainvectordbFreeTrialUNSPECIFIED.lance/data/f7f2d41b-37e0-4677-a12a-d1e3b6bd7a25.lance\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-3d580e51-7483-4160-bf44-ce005cd26f33.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-9d24651c-9163-4870-ad1b-051af848c359.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-eba77257-aeed-4e49-86b6-364959eff341.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-934e3358-87eb-4efa-b3b1-72c3b09f6894.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-89fd1114-2c5f-474e-bfed-365c73b187fc.txn\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-4-7145968909395871742.arrow\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/3f12a079-82df-41db-831a-13111d61b135.lance\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/aae6d80e-eca5-46ce-a2a2-66ce57c94911.lance\nindex/lancedb/cWebui_LiteLLMNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/cf95b38f-44b1-46e0-b48c-d25a00fe07ed.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/_transactions/0-b6db3bbc-6fc5-4dab-a8d3-c7ed4d43e99c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_TransformersJsEmbeddingsProviderall-MiniLM-L6-v2.lance/data/05375ef0-8f10-4a06-af68-64af1945e3ed.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/10.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/100.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/101.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/102.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/103.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/104.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/105.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/106.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/11.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/12.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/13.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/14.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/15.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/16.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/17.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/18.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/19.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/20.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/21.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/22.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/23.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/24.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/25.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/26.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/27.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/28.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/29.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/30.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/31.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/32.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/33.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/34.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/35.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/36.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/37.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/38.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/39.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/40.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/41.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/42.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/43.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/44.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/45.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/46.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/47.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/48.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/49.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/50.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/51.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/52.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/53.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/54.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/55.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/56.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/57.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/58.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/59.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/6.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/60.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/61.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/62.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/63.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/64.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/65.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/66.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/67.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/68.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/69.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/7.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/70.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/71.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/72.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/73.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/74.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/75.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/76.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/77.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/78.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/79.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/8.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/80.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/81.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/82.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/83.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/84.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/85.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/86.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/87.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/88.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/89.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/9.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/90.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/91.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/92.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/93.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/94.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/95.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/96.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/97.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/98.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/99.manifest\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-468e77d5-db40-4271-8af9-bbaa1eb30fe3.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-ed42dc95-4d3c-41af-b1cf-aabb388eaa29.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/10-74888248-52ae-43f6-8c8c-619119b9f7e6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/100-6e7bddd2-02aa-451f-b655-424ad7410d87.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/101-b2015097-7fd1-498b-a532-780e5618cc9c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/102-536557e3-3869-46f8-a216-f97864c903cb.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/103-4151fc16-3df4-4471-b811-f71307d1dec2.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/104-cc7a7ab5-96c3-43e7-a6ea-fd34e63eec8b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/105-08a8f106-dd7b-4cab-a76e-50470bedaa72.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/11-c6213032-117b-477d-8bf0-ddbbc6583044.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/12-91071180-b423-4373-bb71-677a336c70ae.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/13-8aeb0d60-61e0-43ed-a5ab-be440b0db8a3.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/14-469299f3-41bf-4e52-93b9-5f862f3a612a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/15-f228b455-d29b-432b-a72e-ac8ff136c552.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/16-74c0b079-5f72-4da6-99ef-120722c8a0e9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/17-a7c5cfb6-7bc7-4c64-a49a-1e1c809a5d1d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/18-9229d869-6c69-47e4-bae6-af4c712494b7.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/19-876d9abb-ed84-4229-9a9b-b7b1aca375dc.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-f83dae76-79ec-4ca8-b943-6da9bdee3c74.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/20-fff8751a-ecc3-4397-9e93-3c365e24bc97.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/21-942a0c35-da16-4635-a1d6-ba054ca03185.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/22-a3b7e027-6ac4-4979-ae03-4b265cba00da.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/23-9bdee7db-901f-492e-a176-66d16547465b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/24-3933db4c-42b5-48ee-93cb-145cb648b5be.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/25-894a625c-b19f-4f2b-8246-345ad1d65ccd.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/26-a86c2d51-5bc5-4f0c-8940-952094ac8f36.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/27-87aae4bf-68a5-43b9-843a-f5611c71aec6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/28-faf4a5fa-846f-41ee-b111-1807cfac42b6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/29-eb20ca53-cffd-4b92-b858-8ddd802c902b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-7ff31105-7c00-4f33-877d-0291e3e22f00.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/30-b6803304-2850-48e6-9d5b-c1f393a32c03.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/31-766bc588-1f9b-4df7-addb-5118f3844978.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/32-5e87077b-a8fd-4ef9-8343-8301b4afb1db.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/33-7693d89b-57a2-4521-8bc5-a76c63feb939.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/34-035230a9-f57f-4c06-8bac-43f2bfc6f97e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/35-6e8df5f5-36f0-445b-b8c9-3e80e00c408f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/36-73fb5481-bbc3-4671-819e-9394a9a6a0b1.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/37-8547c318-9d4e-44d9-b7f5-34f8ab2c8e07.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/38-613ced78-c5a6-4dda-a56a-f19bcfbaa6dd.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/39-916e4b2c-4bcb-40ae-910f-bd03f15fe845.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-504bb725-2a44-42e3-9ab2-f3f63bcd901e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/40-dacdeba6-c7db-4761-a908-c68c7e058410.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/41-39682dfd-f578-46a1-88dc-ed1bd010dd32.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/42-4d88d8a2-1d37-4a5f-9841-1bce9c5e446c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/43-26f91edd-59c5-4981-bb06-079c4c58fd5b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/44-8b384d44-19c2-41be-9744-c9310e9e476d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/45-b1ffcfcd-6d5c-477a-b1fe-b54ad03ca9d9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/46-57b39e68-980b-4a6b-bb9e-4099babfdc01.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/47-8e162208-df3b-43c1-bcfd-965695c7477f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/48-ca711e95-145a-4038-83be-5f9764932b40.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/49-13e2d25b-a872-4d15-9376-c223aa107973.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/5-f41fdd28-57fc-4831-bc21-2653f0f92871.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/50-8b1993bc-d13e-4480-9342-6cd842a5a5ca.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/51-ead36124-ace5-41a3-a545-0c0582938a26.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/52-3bbb0244-e16d-40d5-a971-538f085b337e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/53-c0caa574-1625-4281-9e32-6b402fc59d84.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/54-f1f5036b-5a82-481b-9345-d234f9699d40.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/55-6f2223ab-4e16-47e8-9f4d-fee7ba1ade4d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/56-e9684650-1c8c-480d-816b-4f750d831396.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/57-5d46798a-4e3f-4940-a68f-dd75d03ad7ee.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/58-c4d490eb-ff27-4226-b1cf-c281bf8980c9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/59-97e949c7-b0b8-4a64-bd86-96c398172b0a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/6-f8800850-1e24-47e5-ac20-9f2634718a19.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/60-c9c59135-b389-4720-91e2-a5d0c2894af4.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/61-5ea97e54-8188-409c-884d-4cdd8098eee5.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/62-c6c5b811-9ac5-4068-aa53-9491d4426acd.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/63-97dc12f4-db41-4304-83a6-8eb0f2cca71c.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/64-34f1922b-2f5a-4581-8186-6e011a6e08a9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/65-91acbe83-d1c6-4d64-bb23-1eac20e7b373.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/66-da2c7542-5439-407f-80f7-a03528ec087a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/67-e2bcedda-c029-4f56-a670-14457d59e179.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/68-d9e0b166-d8c3-42eb-a7b6-76ad3ff7b991.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/69-25095690-b542-4d2d-8ad9-5fea3f3e3374.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/7-68f9cd1e-d503-474c-983e-5040c628e095.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/70-dc412984-04d7-4358-867f-4e6510935697.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/71-44bdc649-e77b-42a5-9187-a814dc583a11.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/72-c67f07be-6e8f-4c51-98ed-ef534b407c91.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/73-4d76e217-0570-4252-99e0-371a1ab3bf16.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/74-27505600-6449-4cf6-8703-719c219dfbab.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/75-5623f748-cbea-4dee-b64b-6337c0abf864.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/76-52f77e1f-0b67-4cdf-a5aa-6af4b9e59924.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/77-1f313740-eb31-4bff-ab7e-3c0f5c599126.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/78-024beb90-b2b3-45c5-a8a8-94eebcd08d07.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/79-6302504e-a832-4c29-9fed-1014fb447e64.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/8-75900a7d-24ae-491f-bac4-0f16b3bd9032.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/80-0037533b-3d77-4834-af24-6afb481341a0.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/81-409b94ee-73e7-4d2f-befa-e8cc737a3931.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/82-fcfc0956-5cb3-4c9c-bec9-4b99e33f5192.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/83-7443c936-7d28-486e-915c-5e620634a854.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/84-61f8358f-1367-49db-b11d-b6dec046d56f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/85-8a9dfcb7-3be3-4a83-9d57-c03487cc7162.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/86-a2bf971c-ef86-4789-8111-efd139e50c8e.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/87-41a32a17-89ee-4906-8548-3309c7b2fe49.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/88-a7e747c9-0f4d-404c-99ed-64d2c9b72d5f.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/89-dc5034e8-78e9-475b-9fef-0be64e60a67d.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/9-33a4a90b-ff99-4044-a47c-415a2d954bde.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/90-ee1904d9-e3f4-402e-aa39-9a9a5ca583cc.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/91-ea9b6e0f-7776-4f54-ac88-930e68091df9.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/92-682fde34-cadc-4172-9541-521829b9c36b.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/93-1fdbc2ae-f23c-4c8a-af5b-e06d5b055bd0.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/94-4ca5497f-b44b-4a13-a352-99555bb3c3ec.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/95-e6b68c6a-0ec8-4ce4-a1ec-50c75c35d832.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/96-8ec568c8-d004-48c8-9bc6-1631069ba2d6.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/97-c9b87012-f0e5-4bb1-916d-bdff158added.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/98-f2379db7-89df-4ad7-ae15-eb380faff829.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/99-08d2bd59-0373-4db7-a01c-658bb37bda9a.txn\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-10-549925515030042835.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-11-12312290082990278072.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-12-12546863215572302023.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-13-8607086708791890548.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-14-7282690490916650355.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-15-13222122184769230605.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-69-5162146069800354050.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-7-438018867623872040.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-70-2700198863766637416.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-73-16813622875355296403.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-76-16458288028775487944.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-8-2681837767972520945.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-9-10246354014714756532.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-102-6397084758809297613.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-61-17895828905791776150.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-64-14066344455790186079.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-67-6555237003325910429.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-68-15830087394093568438.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-75-13561457464375439557.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/1-96-4459926547751262314.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/3-66-14500712657653123367.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-22-14071483736087719395.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-23-12613990763145386882.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-24-12814949837301935385.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-25-17329030441548199866.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-28-9128495002814127692.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-30-11131948704441124328.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-31-15645374567477436001.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-32-14803077281707590285.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-33-10661437038108867062.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-34-9658603270659196864.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-35-14961367826904637998.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-38-17219353825538434573.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-39-10775335505413791967.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-40-3787116558452784214.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-41-13719717179808141218.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-42-3209286349079787144.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-43-13787602717360574820.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-44-6559160351853226391.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-45-8806561158590654924.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-50-5270997752933395436.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-51-10426699983941174985.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-52-18314369573543888363.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-53-13824656193748262753.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-54-6588177187933090424.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-55-4336790527423617822.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/6-56-5493389883289445430.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/8-101-10408064738517512984.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/8-97-5780112088545781433.arrow\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/000fd065-17c8-42f8-9ca9-bc83855d9afa.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/0b42d100-13d1-4f47-ae79-08772dda496d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/124330b4-6838-433f-8a7c-4c1e22f7867f.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/2225f1c7-cb6d-40c7-a0e3-61827aacafd3.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/2eabb10f-576c-4f7d-a9da-d7fb19cc8be3.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/3129b869-7f8a-4bd2-914a-6f918d73b62d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/4f471def-79b4-4578-9712-b68c9fadf64d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/5f41354f-2ec0-4b18-94f1-93c629bd9398.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/625daaa7-de0f-4d48-8f38-fec360d0bf70.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/680d8198-6896-470f-866e-869fa4112a43.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/68a8ce32-58e0-46b0-940b-8ce6ec403ce8.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/6a09ffea-7af0-43dc-bc27-9b5e5dbab190.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/70ee8ae4-df5d-42e8-8894-f1623849f9e1.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/77d35ded-96ed-462a-89eb-3430c06f652e.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/78e2e7af-9aea-4c6d-b8d7-0e781352b26d.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/7cfd0d58-2891-4083-90aa-59528e03f0da.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/7d390a49-3449-49d1-8439-ade3ec1fe5d0.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/817d8ae5-b343-44ba-9b5c-3fabf7dbb19c.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/879f0778-4f37-4525-bc28-84c4f32e02c5.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/8e416056-04cb-49c4-9445-fff8aff245b0.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/90bb5d76-4226-4d63-bac4-952e107375f3.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/92b44b83-1898-4fd2-8601-f1dc3ec55ce9.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/961445ff-2970-40ce-abe8-54b7d2e5e861.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/9c4a3fc9-9606-4a59-b6b8-db10d516c249.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/c537bf3d-5adb-45df-ac01-43aac72922fe.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/cd83ef8e-fa82-47bb-aac6-ad6557bde697.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/cea08ee9-3beb-4e07-97bb-4f2e19339132.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/dbea38dc-f9c7-4849-9152-aa1e1e0e9f6c.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/ef62888f-2799-498a-9c37-3cab12834d8a.lance\nindex/lancedb/cUsersMarioESCDocumentsNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/ff235532-adb7-45d1-82d3-32cd4f9981c6.lance\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-8a60bb17-11e2-4ca3-bb0c-5e41e8070ceb.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-56fa1033-6fa1-4bdc-ba64-3f2adbcba1cb.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-3cd7901b-86db-475f-86fd-32e0c714bdc5.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-d0bab694-ed93-4f4b-8d6f-170fcecab7dc.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-c6bf5636-040c-4598-9269-3f6425b58684.txn\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-3-17773003357296133812.arrow\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/3fb7fd30-4cce-45f9-a8b8-af059554ac7f.lance\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/9e046747-cd1c-4467-94f6-75223162f66c.lance\nindex/lancedb/cUsersMarioESCDocumentsClineEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/a85b50df-2ef6-41aa-859e-32489705f205.lance\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-de7cce48-cb3d-444f-9b32-dee3ad3eb6c1.txn\nindex/lancedb/cUsersMarioESCDocumentsBlackboxNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/1740f3f9-e724-4192-814d-583d0f7495a6.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/3.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/4.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/5.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/6.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/7.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-ef1c6b3b-350f-4432-9375-3d76d092a404.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-3b5c1bb1-3cb6-4e98-ada1-f10d0214c399.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/2-dbc75959-dbb2-4e9c-8fc1-a2e20acc8823.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/3-83dc829f-6d24-43cf-a481-816a65cbc12f.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/4-63b9dd84-d57e-42ea-a31d-ccb5166a35ca.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/5-d9298404-8d92-4b65-a4b0-d33d5c206135.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/6-a17d2425-2c91-4973-a6ba-fb0afdc973c9.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_deletions/0-5-4654674215173510631.arrow\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/1fe0f465-ae0c-4c85-8407-68ec4c6d9174.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/430e8a57-82cc-43ea-b254-de039ccd494c.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/6de441e4-1e69-4b3c-978c-aba04b001400.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/8d940b7a-5eae-44ce-8f65-55b61b2fdb57.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestToolbox_ScriptNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/afaa4b83-e6ef-44b3-af58-e8d6415c74d2.lance\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-8980a58a-e585-4ee0-be3c-4fdaf601739f.txn\nindex/lancedb/cUsersMarioESCDocumentsAide_TestHDM_CSHDMFReportNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/c981262a-aa8f-464f-b510-f8dd5cbb9e20.lance\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/2.manifest\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-87d8323c-4cd3-47bc-8ad9-c2220bd2c8dd.txn\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/1-6e0d8127-0b62-46f9-999b-e974ea2d0378.txn\nindex/lancedb/cUsersMarioESCDocumentsAideEDNC_CSNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/a33ef148-3f84-405f-91cb-3aa8eae25ba8.lance\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_latest.manifest\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_versions/1.manifest\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/_transactions/0-f3f06a5f-8199-4eca-bd7b-52c4205542c5.txn\nindex/lancedb/cUsersMarioESC.aiderNONEvectordb_FreeTrialEmbeddingsProvidervoyage-code-2.lance/data/68d6b933-699e-46af-8dcc-cc85a1311d3e.lance\ndev_data/0.1.0/\ndev_data/0.2.0/\n.migrations/add_signature_column\n.migrations/delete_duplicate_code_snippets\n.migrations/lancedb_sqlite_artifact_id_column\n.migrations/sqlite_delete_docs_with_no_embeddingsProviderId\n.migrations/sqlite_modify_docs_columns_and_copy_to_config\n\n<assistant>\nNo se encontró el archivo src/main.py. ¿Hay algo más en lo que pueda ayudarte?\n\n<user>\n```config.json\n{\n  \"models\": [\n    {\n      \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"GPT-4o (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Llama3.1 70b (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"llama3.1-70b\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Codestral (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"codestral-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"title\": \"Grok Beta\",\n      \"model\": \"grok-beta\",\n      \"contextLength\": 128000,\n      \"apiKey\": \"************************************************************************************\",\n      \"provider\": \"xAI\"\n    },\n    {\n      \"apiKey\": \"XfeFh79plKmcdMWwx2kUVBDTMV9Og7ey\",\n      \"title\": \"Codestral (1)\",\n      \"model\": \"codestral-latest\",\n      \"provider\": \"mistral\"\n    }\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Tab Autocomplete\",\n    \"provider\": \"free-trial\",\n    \"model\": \"codestral-latest\"\n  },\n  \"customCommands\": [\n    {\n      \"name\": \"test\",\n      \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n      \"description\": \"Write unit tests for highlighted code\"\n    }\n  ],\n  \"contextProviders\": [\n    {\n      \"name\": \"code\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"docs\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"diff\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"terminal\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"problems\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"folder\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"codebase\",\n      \"params\": {}\n    }\n  ],\n  \"slashCommands\": [\n    {\n      \"name\": \"share\",\n      \"description\": \"Export the current chat session to markdown\"\n    },\n    {\n      \"name\": \"cmd\",\n      \"description\": \"Generate a shell command\"\n    },\n    {\n      \"name\": \"commit\",\n      \"description\": \"Generate a git commit message\"\n    }\n  ],\n  \"embeddingsProvider\": {\n    \"provider\": \"free-trial\"\n  },\n  \"reranker\": {\n    \"name\": \"free-trial\"\n  }\n}\n```\n\nconfig.json lee este\n\n","completion":"El archivo `config.json` contiene la configuración para varios modelos de inteligencia artificial, incluyendo detalles sobre sus proveedores, claves API, y mensajes del sistema. También incluye configuraciones para comandos personalizados, proveedores de contexto, comandos de barra diagonal, proveedor de incrustaciones y un clasificador.\n\nSi necesitas modificar algún aspecto de esta configuración o agregar nuevos modelos, por favor indícalo.","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:58:06.554Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in chat mode.\n\n  If the user asks to make changes to files offer that they can use the Apply Button on the code block, or switch to Agent Mode to make the suggested updates automatically.\n  If needed consisely explain to the user they can switch to agent mode using the Mode Selector dropdown and provide no other details.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nNo se encontró el archivo src/main.py. ¿Hay algo más en lo que pueda ayudarte?\n\n<user>\n```config.json\n{\n  \"models\": [\n    {\n      \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"GPT-4o (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Llama3.1 70b (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"llama3.1-70b\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Codestral (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"codestral-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"title\": \"Grok Beta\",\n      \"model\": \"grok-beta\",\n      \"contextLength\": 128000,\n      \"apiKey\": \"************************************************************************************\",\n      \"provider\": \"xAI\"\n    },\n    {\n      \"apiKey\": \"XfeFh79plKmcdMWwx2kUVBDTMV9Og7ey\",\n      \"title\": \"Codestral (1)\",\n      \"model\": \"codestral-latest\",\n      \"provider\": \"mistral\"\n    }\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Tab Autocomplete\",\n    \"provider\": \"free-trial\",\n    \"model\": \"codestral-latest\"\n  },\n  \"customCommands\": [\n    {\n      \"name\": \"test\",\n      \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n      \"description\": \"Write unit tests for highlighted code\"\n    }\n  ],\n  \"contextProviders\": [\n    {\n      \"name\": \"code\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"docs\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"diff\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"terminal\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"problems\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"folder\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"codebase\",\n      \"params\": {}\n    }\n  ],\n  \"slashCommands\": [\n    {\n      \"name\": \"share\",\n      \"description\": \"Export the current chat session to markdown\"\n    },\n    {\n      \"name\": \"cmd\",\n      \"description\": \"Generate a shell command\"\n    },\n    {\n      \"name\": \"commit\",\n      \"description\": \"Generate a git commit message\"\n    }\n  ],\n  \"embeddingsProvider\": {\n    \"provider\": \"free-trial\"\n  },\n  \"reranker\": {\n    \"name\": \"free-trial\"\n  }\n}\n```\n\nconfig.json lee este\n\n<assistant>\nEl archivo `config.json` contiene la configuración para varios modelos de inteligencia artificial, incluyendo detalles sobre sus proveedores, claves API, y mensajes del sistema. También incluye configuraciones para comandos personalizados, proveedores de contexto, comandos de barra diagonal, proveedor de incrustaciones y un clasificador.\n\nSi necesitas modificar algún aspecto de esta configuración o agregar nuevos modelos, por favor indícalo.\n\n<user>\nexplicame le ts\n\n","completion":"El archivo `config.json` es un archivo de configuración en formato JSON que contiene varias secciones y parámetros para configurar diferentes aspectos de una aplicación o sistema. A continuación, se explica cada sección y sus componentes:\n\n### Sección `models`\nEsta sección define una lista de modelos de inteligencia artificial que pueden ser utilizados. Cada modelo tiene los siguientes atributos:\n- `title`: El nombre del modelo.\n- `provider`: El proveedor del modelo (puede ser un proveedor de prueba gratuita o un proveedor específico).\n- `model`: El identificador del modelo.\n- `systemMessage`: Un mensaje del sistema que describe el propósito del modelo.\n- `apiKey`: (opcional) La clave API para acceder al modelo.\n- `contextLength`: (opcional) La longitud del contexto que el modelo puede manejar.\n\nEjemplo:\n```json\n{\n  \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n  \"provider\": \"free-trial\",\n  \"model\": \"claude-3-5-sonnet-latest\",\n  \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n}\n```\n\n### Sección `tabAutocompleteModel`\nDefine el modelo utilizado para la autocompletación de pestañas:\n- `title`: El nombre del modelo.\n- `provider`: El proveedor del modelo.\n- `model`: El identificador del modelo.\n\nEjemplo:\n```json\n{\n  \"title\": \"Tab Autocomplete\",\n  \"provider\": \"free-trial\",\n  \"model\": \"codestral-latest\"\n}\n```\n\n### Sección `customCommands`\nDefine una lista de comandos personalizados que pueden ser utilizados en la aplicación:\n- `name`: El nombre del comando.\n- `prompt`: El prompt asociado al comando.\n- `description`: Una descripción del comando.\n\nEjemplo:\n```json\n{\n  \"name\": \"test\",\n  \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n  \"description\": \"Write unit tests for highlighted code\"\n}\n```\n\n### Sección `contextProviders`\nDefine una lista de proveedores de contexto que pueden ser utilizados:\n- `name`: El nombre del proveedor de contexto.\n- `params`: Parámetros adicionales para el proveedor de contexto.\n\nEjemplo:\n```json\n{\n  \"name\": \"code\",\n  \"params\": {}\n}\n```\n\n### Sección `slashCommands`\nDefine una lista de comandos de barra diagonal que pueden ser utilizados:\n- `name`: El nombre del comando.\n- `description`: Una descripción del comando.\n\nEjemplo:\n```json\n{\n  \"name\": \"share\",\n  \"description\": \"Export the current chat session to markdown\"\n}\n```\n\n### Sección `embeddingsProvider`\nDefine el proveedor de incrustaciones:\n- `provider`: El nombre del proveedor.\n\nEjemplo:\n```json\n{\n  \"provider\": \"free-trial\"\n}\n```\n\n### Sección `reranker`\nDefine el clasificador:\n- `name`: El nombre del clasificador.\n\nEjemplo:\n```json\n{\n  \"name\": \"free-trial\"\n}\n```\n\n### Resumen\nEl archivo `config.json` es una configuración centralizada que permite definir y gestionar múltiples aspectos de una aplicación, incluyendo modelos de IA, comandos personalizados, proveedores de contexto, comandos de barra diagonal, proveedores de incrustaciones y clasificadores. Cada sección está diseñada para ser modular y fácil de extender o modificar según sea necesario.","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-03T23:58:44.278Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in chat mode.\n\n  If the user asks to make changes to files offer that they can use the Apply Button on the code block, or switch to Agent Mode to make the suggested updates automatically.\n  If needed consisely explain to the user they can switch to agent mode using the Mode Selector dropdown and provide no other details.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nNo se encontró el archivo src/main.py. ¿Hay algo más en lo que pueda ayudarte?\n\n<user>\n```config.json\n{\n  \"models\": [\n    {\n      \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"GPT-4o (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Llama3.1 70b (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"llama3.1-70b\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Codestral (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"codestral-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"title\": \"Grok Beta\",\n      \"model\": \"grok-beta\",\n      \"contextLength\": 128000,\n      \"apiKey\": \"************************************************************************************\",\n      \"provider\": \"xAI\"\n    },\n    {\n      \"apiKey\": \"XfeFh79plKmcdMWwx2kUVBDTMV9Og7ey\",\n      \"title\": \"Codestral (1)\",\n      \"model\": \"codestral-latest\",\n      \"provider\": \"mistral\"\n    }\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Tab Autocomplete\",\n    \"provider\": \"free-trial\",\n    \"model\": \"codestral-latest\"\n  },\n  \"customCommands\": [\n    {\n      \"name\": \"test\",\n      \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n      \"description\": \"Write unit tests for highlighted code\"\n    }\n  ],\n  \"contextProviders\": [\n    {\n      \"name\": \"code\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"docs\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"diff\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"terminal\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"problems\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"folder\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"codebase\",\n      \"params\": {}\n    }\n  ],\n  \"slashCommands\": [\n    {\n      \"name\": \"share\",\n      \"description\": \"Export the current chat session to markdown\"\n    },\n    {\n      \"name\": \"cmd\",\n      \"description\": \"Generate a shell command\"\n    },\n    {\n      \"name\": \"commit\",\n      \"description\": \"Generate a git commit message\"\n    }\n  ],\n  \"embeddingsProvider\": {\n    \"provider\": \"free-trial\"\n  },\n  \"reranker\": {\n    \"name\": \"free-trial\"\n  }\n}\n```\n\nconfig.json lee este\n\n<assistant>\nEl archivo `config.json` contiene la configuración para varios modelos de inteligencia artificial, incluyendo detalles sobre sus proveedores, claves API, y mensajes del sistema. También incluye configuraciones para comandos personalizados, proveedores de contexto, comandos de barra diagonal, proveedor de incrustaciones y un clasificador.\n\nSi necesitas modificar algún aspecto de esta configuración o agregar nuevos modelos, por favor indícalo.\n\n<user>\nexplicame le ts\n\n<assistant>\nEl archivo `config.json` es un archivo de configuración en formato JSON que contiene varias secciones y parámetros para configurar diferentes aspectos de una aplicación o sistema. A continuación, se explica cada sección y sus componentes:\n\n### Sección `models`\nEsta sección define una lista de modelos de inteligencia artificial que pueden ser utilizados. Cada modelo tiene los siguientes atributos:\n- `title`: El nombre del modelo.\n- `provider`: El proveedor del modelo (puede ser un proveedor de prueba gratuita o un proveedor específico).\n- `model`: El identificador del modelo.\n- `systemMessage`: Un mensaje del sistema que describe el propósito del modelo.\n- `apiKey`: (opcional) La clave API para acceder al modelo.\n- `contextLength`: (opcional) La longitud del contexto que el modelo puede manejar.\n\nEjemplo:\n```json\n{\n  \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n  \"provider\": \"free-trial\",\n  \"model\": \"claude-3-5-sonnet-latest\",\n  \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n}\n```\n\n### Sección `tabAutocompleteModel`\nDefine el modelo utilizado para la autocompletación de pestañas:\n- `title`: El nombre del modelo.\n- `provider`: El proveedor del modelo.\n- `model`: El identificador del modelo.\n\nEjemplo:\n```json\n{\n  \"title\": \"Tab Autocomplete\",\n  \"provider\": \"free-trial\",\n  \"model\": \"codestral-latest\"\n}\n```\n\n### Sección `customCommands`\nDefine una lista de comandos personalizados que pueden ser utilizados en la aplicación:\n- `name`: El nombre del comando.\n- `prompt`: El prompt asociado al comando.\n- `description`: Una descripción del comando.\n\nEjemplo:\n```json\n{\n  \"name\": \"test\",\n  \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n  \"description\": \"Write unit tests for highlighted code\"\n}\n```\n\n### Sección `contextProviders`\nDefine una lista de proveedores de contexto que pueden ser utilizados:\n- `name`: El nombre del proveedor de contexto.\n- `params`: Parámetros adicionales para el proveedor de contexto.\n\nEjemplo:\n```json\n{\n  \"name\": \"code\",\n  \"params\": {}\n}\n```\n\n### Sección `slashCommands`\nDefine una lista de comandos de barra diagonal que pueden ser utilizados:\n- `name`: El nombre del comando.\n- `description`: Una descripción del comando.\n\nEjemplo:\n```json\n{\n  \"name\": \"share\",\n  \"description\": \"Export the current chat session to markdown\"\n}\n```\n\n### Sección `embeddingsProvider`\nDefine el proveedor de incrustaciones:\n- `provider`: El nombre del proveedor.\n\nEjemplo:\n```json\n{\n  \"provider\": \"free-trial\"\n}\n```\n\n### Sección `reranker`\nDefine el clasificador:\n- `name`: El nombre del clasificador.\n\nEjemplo:\n```json\n{\n  \"name\": \"free-trial\"\n}\n```\n\n### Resumen\nEl archivo `config.json` es una configuración centralizada que permite definir y gestionar múltiples aspectos de una aplicación, incluyendo modelos de IA, comandos personalizados, proveedores de contexto, comandos de barra diagonal, proveedores de incrustaciones y clasificadores. Cada sección está diseñada para ser modular y fácil de extender o modificar según sea necesario.\n\n<user>\n```types/core/index.d.ts\n\ndeclare global {\n  import Parser from \"web-tree-sitter\";\n  import { GetGhTokenArgs } from \"./protocol/ide\";\n  declare global {\n    interface Window {\n      ide?: \"vscode\";\n      windowId: string;\n      serverUrl: string;\n      vscMachineId: string;\n      vscMediaUrl: string;\n      fullColorTheme?: {\n        rules?: {\n          token?: string;\n          foreground?: string;\n        }[];\n      };\n      colorThemeName?: string;\n      workspacePaths?: string[];\n      postIntellijMessage?: (\n        messageType: string,\n        data: any,\n        messageIde: string,\n      ) => void;\n    }\n  }\n  \n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n  \n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n  \n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status:\n      | \"loading\"\n      | \"indexing\"\n      | \"done\"\n      | \"failed\"\n      | \"paused\"\n      | \"disabled\"\n      | \"cancelled\";\n    debugInfo?: string;\n  }\n  \n  // This is more or less a V2 of IndexingProgressUpdate for docs etc.\n  export interface IndexingStatus {\n    id: string;\n    type: \"docs\";\n    progress: number;\n    description: string;\n    status: \"indexing\" | \"complete\" | \"paused\" | \"failed\" | \"aborted\" | \"pending\";\n    embeddingsProviderId: string;\n    isReindexing?: boolean;\n    debugInfo?: string;\n    title: string;\n    icon?: string;\n    url?: string;\n  }\n  \n  export type PromptTemplateFunction = (\n    history: ChatMessage[],\n    otherData: Record<string, string>,\n  ) => string | ChatMessage[];\n  \n  export type PromptTemplate = string | PromptTemplateFunction;\n  \n  export interface ILLM extends LLMOptions {\n    get providerName(): string;\n  \n    uniqueId: string;\n    model: string;\n  \n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    llmLogger?: ILLMLogger;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n    cacheBehavior?: CacheBehavior;\n  \n    deployment?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n  \n    // Embedding options\n    embeddingId: string;\n    maxEmbeddingChunkSize: number;\n    maxEmbeddingBatchSize: number;\n  \n    complete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<string>;\n  \n    streamComplete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n  \n    streamFim(\n      prefix: string,\n      suffix: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n  \n    streamChat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n  \n    chat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n  \n    embed(chunks: string[]): Promise<number[][]>;\n  \n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  \n    countTokens(text: string): number;\n  \n    supportsImages(): boolean;\n  \n    supportsCompletions(): boolean;\n  \n    supportsPrefill(): boolean;\n  \n    supportsFim(): boolean;\n  \n    listModels(): Promise<string[]>;\n  \n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n  \n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n  \n  export interface ContextProviderDescription {\n    title: ContextProviderName;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n    dependsOnIndexing?: boolean;\n  }\n  \n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n  \n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: ILLM;\n    reranker: ILLM | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n  \n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n  \n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n  \n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n  \n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n  \n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n  \n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n  \n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n  \n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n  \n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n  \n  export interface Session {\n    sessionId: string;\n    title: string;\n    workspaceDirectory: string;\n    history: ChatHistoryItem[];\n  }\n  \n  export interface SessionMetadata {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n  \n  export interface RangeInFile {\n    filepath: string;\n    range: Range;\n  }\n  \n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n  \n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n  \n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  \n  export interface Position {\n    line: number;\n    character: number;\n  }\n  \n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n  \n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n  \n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n  \n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\" | \"tool\";\n  \n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n  \n  export type MessageContent = string | MessagePart[];\n  \n  export interface ToolCall {\n    id: string;\n    type: \"function\";\n    function: {\n      name: string;\n      arguments: string;\n    };\n  }\n  \n  export interface ToolCallDelta {\n    id?: string;\n    type?: \"function\";\n    function?: {\n      name?: string;\n      arguments?: string;\n    };\n  }\n  \n  export interface ToolResultChatMessage {\n    role: \"tool\";\n    content: string;\n    toolCallId: string;\n  }\n  \n  export interface UserChatMessage {\n    role: \"user\";\n    content: MessageContent;\n  }\n  \n  export interface AssistantChatMessage {\n    role: \"assistant\";\n    content: MessageContent;\n    toolCalls?: ToolCallDelta[];\n  }\n  \n  export interface SystemChatMessage {\n    role: \"system\";\n    content: string;\n  }\n  \n  export type ChatMessage =\n    | UserChatMessage\n    | AssistantChatMessage\n    | SystemChatMessage\n    | ToolResultChatMessage;\n  \n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n  \n  export type ContextItemUriTypes = \"file\" | \"url\";\n  \n  export interface ContextItemUri {\n    type: ContextItemUriTypes;\n    value: string;\n  }\n  \n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n    uri?: ContextItemUri;\n    hidden?: boolean;\n  }\n  \n  export interface ContextItemWithId extends ContextItem {\n    id: ContextItemId;\n  }\n  \n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n  \n  export interface SymbolWithRange extends RangeInFile {\n    name: string;\n    type: Parser.SyntaxNode[\"type\"];\n    content: string;\n  }\n  \n  export type FileSymbolMap = Record<string, SymbolWithRange[]>;\n  \n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n  \n  type MessageModes = \"chat\" | \"edit\";\n  \n  export type ToolStatus =\n    | \"generating\"\n    | \"generated\"\n    | \"calling\"\n    | \"done\"\n    | \"errored\"\n    | \"canceled\";\n  \n  // Will exist only on \"assistant\" messages with tool calls\n  interface ToolCallState {\n    toolCallId: string;\n    toolCall: ToolCall;\n    status: ToolStatus;\n    parsedArgs: any;\n    output?: ContextItem[];\n  }\n  \n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    contextItems: ContextItemWithId[];\n    editorState?: any;\n    modifiers?: InputModifiers;\n    promptLogs?: PromptLog[];\n    toolCallState?: ToolCallState;\n    isGatheringContext?: boolean;\n  }\n  \n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n    model?: string;\n  }\n  \n  export type ToastType = \"info\" | \"error\" | \"warning\";\n  \n  export interface LLMInteractionBase {\n    interactionId: string;\n    timestamp: number;\n  }\n  \n  export interface LLMInteractionStartChat extends LLMInteractionBase {\n    kind: \"startChat\";\n    messages: ChatMessage[];\n    options: CompletionOptions;\n  }\n  \n  export interface LLMInteractionStartComplete extends LLMInteractionBase {\n    kind: \"startComplete\";\n    prompt: string;\n    options: CompletionOptions;\n  }\n  \n  export interface LLMInteractionStartFim extends LLMInteractionBase {\n    kind: \"startFim\";\n    prefix: string;\n    suffix: string;\n    options: CompletionOptions;\n  }\n  \n  export interface LLMInteractionChunk extends LLMInteractionBase {\n    kind: \"chunk\";\n    chunk: string;\n  }\n  \n  export interface LLMInteractionMessage extends LLMInteractionBase {\n    kind: \"message\";\n    message: ChatMessage;\n  }\n  \n  export interface LLMInteractionEnd extends LLMInteractionBase {\n    promptTokens: number;\n    generatedTokens: number;\n    thinkingTokens: number;\n  }\n  \n  export interface LLMInteractionSuccess extends LLMInteractionEnd {\n    kind: \"success\";\n  }\n  \n  export interface LLMInteractionCancel extends LLMInteractionEnd {\n    kind: \"cancel\";\n  }\n  \n  export interface LLMInteractionError extends LLMInteractionEnd {\n    kind: \"error\";\n    name: string;\n    message: string;\n  }\n  \n  export type LLMInteractionItem =\n    | LLMInteractionStartChat\n    | LLMInteractionStartComplete\n    | LLMInteractionStartFim\n    | LLMInteractionChunk\n    | LLMInteractionMessage\n    | LLMInteractionSuccess\n    | LLMInteractionCancel\n    | LLMInteractionError;\n  \n  // When we log a LLM interaction, we want to add the interactionId and timestamp\n  // in the logger code, so we need a type that omits these members from *each*\n  // member of the union. This can be done by using the distributive behavior of\n  // conditional types in Typescript.\n  //\n  // www.typescriptlang.org/docs/handbook/2/conditional-types.html#distributive-conditional-types\n  // https://stackoverflow.com/questions/57103834/typescript-omit-a-property-from-all-interfaces-in-a-union-but-keep-the-union-s\n  type DistributiveOmit<T, K extends PropertyKey> = T extends unknown\n    ? Omit<T, K>\n    : never;\n  \n  export type LLMInteractionItemDetails = DistributiveOmit<\n    LLMInteractionItem,\n    \"interactionId\" | \"timestamp\"\n  >;\n  \n  export interface ILLMInteractionLog {\n    logItem(item: LLMInteractionItemDetails): void;\n  }\n  \n  export interface ILLMLogger {\n    createInteractionLog(): ILLMInteractionLog;\n  }\n  \n  export interface LLMOptions {\n    model: string;\n  \n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    logger?: ILLMLogger;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n    cacheBehavior?: CacheBehavior;\n  \n    useLegacyCompletionsEndpoint?: boolean;\n  \n    // Embedding options\n    embeddingId?: string;\n    maxEmbeddingChunkSize?: number;\n    maxEmbeddingBatchSize?: number;\n  \n    // Cloudflare options\n    accountId?: string;\n  \n    // Azure options\n    deployment?: string;\n    apiVersion?: string;\n    apiType?: string;\n  \n    // AWS options\n    profile?: string;\n    modelArn?: string;\n  \n    // AWS and GCP Options\n    region?: string;\n  \n    // GCP Options\n    capabilities?: ModelCapability;\n  \n    // GCP and Watsonx Options\n    projectId?: string;\n  \n    // IBM watsonx Options\n    deploymentId?: string;\n  }\n  \n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n  \n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      signal: AbortSignal,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n  \n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n  \n  // IDE\n  \n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n  \n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n  \n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n  \n  export class Thread {\n    name: string;\n    id: number;\n  }\n  \n  export type IdeType = \"vscode\" | \"jetbrains\";\n  \n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n  \n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n  \n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n  \n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n  \n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    pauseCodebaseIndexOnStart: boolean;\n  }\n  \n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n  \n    getIdeSettings(): Promise<IdeSettings>;\n  \n    getDiff(includeUnstaged: boolean): Promise<string[]>;\n  \n    getClipboardContent(): Promise<{ text: string; copiedAt: string }>;\n  \n    isTelemetryEnabled(): Promise<boolean>;\n  \n    getUniqueId(): Promise<string>;\n  \n    getTerminalContents(): Promise<string>;\n  \n    getDebugLocals(threadIndex: number): Promise<string>;\n  \n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n  \n    getAvailableThreads(): Promise<Thread[]>;\n  \n    getWorkspaceDirs(): Promise<string[]>;\n  \n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n  \n    fileExists(filepath: string): Promise<boolean>;\n  \n    writeFile(path: string, contents: string): Promise<void>;\n  \n    showVirtualFile(title: string, contents: string): Promise<void>;\n    openFile(path: string): Promise<void>;\n  \n    openUrl(url: string): Promise<void>;\n  \n    runCommand(command: string): Promise<void>;\n  \n    saveFile(filepath: string): Promise<void>;\n  \n    readFile(filepath: string): Promise<string>;\n  \n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n  \n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n  \n    getCurrentFile(): Promise<\n      | undefined\n      | {\n          isUntitled: boolean;\n          path: string;\n          contents: string;\n        }\n    >;\n  \n    getPinnedFiles(): Promise<string[]>;\n  \n    getSearchResults(query: string): Promise<string>;\n  \n    subprocess(command: string, cwd?: string): Promise<[string, string]>;\n  \n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n  \n    getBranch(dir: string): Promise<string>;\n  \n    getTags(artifactId: string): Promise<IndexTag[]>;\n  \n    getRepoName(dir: string): Promise<string | undefined>;\n  \n    showToast(\n      type: ToastType,\n      message: string,\n      ...otherParams: any[]\n    ): Promise<any>;\n  \n    getGitRootPath(dir: string): Promise<string | undefined>;\n  \n    listDir(dir: string): Promise<[string, FileType][]>;\n  \n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n  \n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n  \n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n  }\n  \n  // Slash Commands\n  \n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n  \n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n  \n  // Config\n  \n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n  \n  type ContextProviderName =\n    | \"diff\"\n    | \"terminal\"\n    | \"debugger\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\"\n    | \"greptile\"\n    | \"outline\"\n    | \"continue-proxy\"\n    | \"highlights\"\n    | \"file\"\n    | \"issue\"\n    | \"repo-map\"\n    | \"url\"\n    | string;\n  \n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"granite\"\n    | \"llama3\"\n    | \"codestral\";\n  \n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n  \n  export interface CacheBehavior {\n    cacheSystemMessage?: boolean;\n    cacheConversation?: boolean;\n  }\n  \n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n  \n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n  \n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n  \n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n  \n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n  \n  interface Prediction {\n    type: \"content\";\n    content:\n      | string\n      | {\n          type: \"text\";\n          text: string;\n        }[];\n  }\n  \n  export interface ToolExtras {\n    ide: IDE;\n    llm: ILLM;\n    fetch: FetchFunction;\n  }\n  \n  export interface Tool {\n    type: \"function\";\n    function: {\n      name: string;\n      description?: string;\n      parameters?: Record<string, any>;\n      strict?: boolean | null;\n    };\n  \n    displayTitle: string;\n    wouldLikeTo: string;\n    readonly: boolean;\n    uri?: string;\n  }\n  \n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    useMmap?: boolean;\n    keepAlive?: number;\n    numGpu?: number;\n    raw?: boolean;\n    stream?: boolean;\n    prediction?: Prediction;\n    tools?: Tool[];\n  }\n  \n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n  \n  export interface ModelDescription {\n    title: string;\n    provider: string;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n    cacheBehavior?: CacheBehavior;\n  }\n  \n  export interface JSONEmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    deployment?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n    maxBatchSize?: number;\n    // AWS options\n    profile?: string;\n  \n    // AWS and GCP Options\n    region?: string;\n  \n    // GCP and Watsonx Options\n    projectId?: string;\n  }\n  \n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: string;\n  }\n  \n  export interface RerankerDescription {\n    name: string;\n    params?: { [key: string]: any };\n  }\n  \n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    transform?: boolean;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useRecentlyEdited: boolean;\n    disableInFiles?: string[];\n    useImports?: boolean;\n    showWhateverWeHaveAtXMs?: number;\n  }\n  \n  interface StdioOptions {\n    type: \"stdio\";\n    command: string;\n    args: string[];\n  }\n  \n  interface WebSocketOptions {\n    type: \"websocket\";\n    url: string;\n  }\n  \n  interface SSEOptions {\n    type: \"sse\";\n    url: string;\n  }\n  \n  type TransportOptions = StdioOptions | WebSocketOptions | SSEOptions;\n  \n  export interface MCPOptions {\n    transport: TransportOptions;\n  }\n  \n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n    showChatScrollbar?: boolean;\n    codeWrap?: boolean;\n  }\n  \n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n  \n  interface ExperimentalModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n    repoMapFileSelection?: string;\n  }\n  \n  export type ApplyStateStatus =\n    | \"streaming\" // Changes are being applied to the file\n    | \"done\" // All changes have been applied, awaiting user to accept/reject\n    | \"closed\"; // All changes have been applied. Note that for new files, we immediately set the status to \"closed\"\n  \n  export interface ApplyState {\n    streamId: string;\n    status?: ApplyStateStatus;\n    numDiffs?: number;\n    filepath?: string;\n    fileContent?: string;\n  }\n  \n  export interface RangeInFileWithContents {\n    filepath: string;\n    range: {\n      start: { line: number; character: number };\n      end: { line: number; character: number };\n    };\n    contents: string;\n  }\n  \n  export type CodeToEdit = RangeInFileWithContents | FileWithContents;\n  \n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n  \n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n  \n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n  \n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n  \n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ExperimentalModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n  \n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n  \n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  \n    /**\n     * If set to true, we will attempt to pull down and install an instance of Chromium\n     * that is compatible with the current version of Puppeteer.\n     * This is needed to crawl a large number of documentation sites that are dynamically rendered.\n     */\n    useChromiumForDocsCrawling?: boolean;\n    useTools?: boolean;\n    modelContextProtocolServers?: MCPOptions[];\n  }\n  \n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n  \n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n  \n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n  \n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n  \n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | ILLM;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | ILLM;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n  \n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: ILLM;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: ILLM;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n    tools: Tool[];\n  }\n  \n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n    tools: Tool[];\n  }\n  \n  // DOCS SUGGESTIONS AND PACKAGE INFO\n  export interface FilePathAndName {\n    path: string;\n    name: string;\n  }\n  \n  export interface PackageFilePathAndName extends FilePathAndName {\n    packageRegistry: string; // e.g. npm, pypi\n  }\n  \n  export type ParsedPackageInfo = {\n    name: string;\n    packageFile: PackageFilePathAndName;\n    language: string;\n    version: string;\n  };\n  \n  export type PackageDetails = {\n    docsLink?: string;\n    docsLinkWarning?: string;\n    title?: string;\n    description?: string;\n    repo?: string;\n    license?: string;\n  };\n  \n  export type PackageDetailsSuccess = PackageDetails & {\n    docsLink: string;\n  };\n  \n  export type PackageDocsResult = {\n    packageInfo: ParsedPackageInfo;\n  } & (\n    | { error: string; details?: never }\n    | { details: PackageDetailsSuccess; error?: never }\n  );\n}\n\nexport {};\n\n```\n\nindex.d.ts ahora este\n\n","completion":"El archivo `index.d.ts` es un archivo de definiciones de tipos para TypeScript. Define interfaces, tipos y declaraciones globales que pueden ser utilizadas en un proyecto TypeScript. A continuación, se explica cada sección y sus componentes:\n\n### Sección `global`\nEsta sección define declaraciones globales que pueden ser utilizadas en cualquier parte del proyecto.\n\n#### Interfaces y Tipos Globales\n- **`Window`**: Define propiedades adicionales que se pueden agregar al objeto `window` en el navegador.\n  ```typescript\n  interface Window {\n    ide?: \"vscode\";\n    windowId: string;\n    serverUrl: string;\n    vscMachineId: string;\n    vscMediaUrl: string;\n    fullColorTheme?: {\n      rules?: {\n        token?: string;\n        foreground?: string;\n      }[];\n    };\n    colorThemeName?: string;\n    workspacePaths?: string[];\n    postIntellijMessage?: (\n      messageType: string,\n      data: any,\n      messageIde: string,\n    ) => void;\n  }\n  ```\n\n- **`ChunkWithoutID`**: Define la estructura de un fragmento de texto sin un identificador.\n  ```typescript\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n  ```\n\n- **`Chunk`**: Extiende `ChunkWithoutID` añadiendo propiedades adicionales.\n  ```typescript\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n  ```\n\n- **`IndexingProgressUpdate`**: Define la estructura de una actualización de progreso de indexación.\n  ```typescript\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status:\n      | \"loading\"\n      | \"indexing\"\n      | \"done\"\n      | \"failed\"\n      | \"paused\"\n      | \"disabled\"\n      | \"cancelled\";\n    debugInfo?: string;\n  }\n  ```\n\n- **`IndexingStatus`**: Define la estructura del estado de indexación.\n  ```typescript\n  export interface IndexingStatus {\n    id: string;\n    type: \"docs\";\n    progress: number;\n    description: string;\n    status: \"indexing\" | \"complete\" | \"paused\" | \"failed\" | \"aborted\" | \"pending\";\n    embeddingsProviderId: string;\n    isReindexing?: boolean;\n    debugInfo?: string;\n    title: string;\n    icon?: string;\n    url?: string;\n  }\n  ```\n\n- **`PromptTemplateFunction`**: Define una función que genera una plantilla de prompt.\n  ```typescript\n  export type PromptTemplateFunction = (\n    history: ChatMessage[],\n    otherData: Record<string, string>,\n  ) => string | ChatMessage[];\n  ```\n\n- **`PromptTemplate`**: Define una plantilla de prompt que puede ser una cadena o una función.\n  ```typescript\n  export type PromptTemplate = string | PromptTemplateFunction;\n  ```\n\n### Interfaces y Tipos para Modelos de IA\n- **`ILLM`**: Define la estructura de un modelo de lenguaje de inteligencia artificial.\n  ```typescript\n  export interface ILLM extends LLMOptions {\n    get providerName(): string;\n    uniqueId: string;\n    model: string;\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    llmLogger?: ILLMLogger;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n    cacheBehavior?: CacheBehavior;\n    deployment?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n    embeddingId: string;\n    maxEmbeddingChunkSize: number;\n    maxEmbeddingBatchSize: number;\n    complete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<string>;\n    streamComplete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n    streamFim(\n      prefix: string,\n      suffix: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n    streamChat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n    chat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n    embed(chunks: string[]): Promise<number[][]>;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n    countTokens(text: string): number;\n    supportsImages(): boolean;\n    supportsCompletions(): boolean;\n    supportsPrefill(): boolean;\n    supportsFim(): boolean;\n    listModels(): Promise<string[]>;\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n  ```\n\n### Interfaces y Tipos para Proveedores de Contexto\n- **`ContextProviderType`**: Define los tipos de proveedores de contexto.\n  ```typescript\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n  ```\n\n- **`ContextProviderDescription`**: Define la estructura de una descripción de proveedor de contexto.\n  ```typescript\n  export interface ContextProviderDescription {\n    title: ContextProviderName;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n    dependsOnIndexing?: boolean;\n  }\n  ```\n\n- **`FetchFunction`**: Define una función de tipo `fetch`.\n  ```typescript\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n  ```\n\n- **`ContextProviderExtras`**: Define las propiedades adicionales que pueden ser pasadas a un proveedor de contexto.\n  ```typescript\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: ILLM;\n    reranker: ILLM | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n  ```\n\n- **`LoadSubmenuItemsArgs`**: Define los argumentos para cargar elementos de submenú.\n  ```typescript\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n  ```\n\n- **`CustomContextProvider`**: Define la estructura de un proveedor de contexto personalizado.\n  ```typescript\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n  ```\n\n### Interfaces y Tipos para Configuración de Indexación de Sitios\n- **`SiteIndexingConfig`**: Define la estructura de la configuración de indexación de sitios.\n  ```typescript\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n  ```\n\n### Interfaces y Tipos para Proveedores de Contexto\n- **`IContextProvider`**: Define la estructura de un proveedor de contexto.\n  ```typescript\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n  ```\n\n### Interfaces y Tipos para Sesiones\n- **`Session`**: Define la estructura de una sesión.\n  ```typescript\n  export interface Session {\n    sessionId: string;\n    title: string;\n    workspaceDirectory: string;\n    history: ChatHistoryItem[];\n  }\n  ```\n\n- **`SessionMetadata`**: Define la estructura de los metadatos de una sesión.\n  ```typescript\n  export interface SessionMetadata {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n  ```\n\n### Interfaces y Tipos para Rangos y Posiciones en Archivos\n- **`RangeInFile`**: Define la estructura de un rango en un archivo.\n  ```typescript\n  export interface RangeInFile {\n    filepath: string;\n    range: Range;\n  }\n  ```\n\n- **`Location`**: Define la estructura de una ubicación en un archivo.\n  ```typescript\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n  ```\n\n- **`FileWithContents`**: Define la estructura de un archivo con su contenido.\n  ```typescript\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n  ```\n\n- **`Range`**: Define la estructura de un rango.\n  ```typescript\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  ```\n\n- **`Position`**: Define la estructura de una posición.\n  ```typescript\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  ```\n\n### Interfaces y Tipos para Ediciones de Archivos\n- **`FileEdit`**: Define la estructura de una edición de archivo.\n  ```typescript\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n  ```\n\n### Interfaces y Tipos para Errores\n- **`ContinueError`**: Define la estructura de un error.\n  ```typescript\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n  ```\n\n### Interfaces y Tipos para Opciones de Completación\n- **`CompletionOptions`**: Define la estructura de las opciones de completación.\n  ```typescript\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n  ```\n\n### Interfaces y Tipos para Mensajes de Chat\n- **`ChatMessageRole`**: Define los roles posibles de un mensaje de chat.\n  ```typescript\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\" | \"tool\";\n  ```\n\n- **`MessagePart`**: Define la estructura de una parte de un mensaje.\n  ```typescript\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n  ```\n\n- **`MessageContent`**: Define el contenido de un mensaje.\n  ```typescript\n  export type MessageContent = string | MessagePart[];\n  ```\n\n- **`ToolCall`**: Define la estructura de una llamada a una herramienta.\n  ```typescript\n  export interface ToolCall {\n    id: string;\n    type: \"function\";\n    function: {\n      name: string;\n      arguments: string;\n    };\n  }\n  ```\n\n- **`ToolCallDelta`**: Define la estructura de un delta de llamada a una herramienta.\n  ```typescript\n  export interface ToolCallDelta {\n    id?: string;\n    type?: \"function\";\n    function?: {\n      name?: string;\n      arguments?: string;\n    };\n  }\n  ```\n\n- **`ToolResultChatMessage`**: Define la estructura de un mensaje de resultado de herramienta.\n  ```typescript\n  export interface ToolResultChatMessage {\n    role: \"tool\";\n    content: string;\n    toolCallId: string;\n  }\n  ```\n\n- **`UserChatMessage`**: Define la estructura de un mensaje de usuario.\n  ```typescript\n  export interface UserChatMessage {\n    role: \"user\";\n    content: MessageContent;\n  }\n  ```\n\n- **`AssistantChatMessage`**: Define la estructura de un mensaje de asistente.\n  ```typescript\n  export interface AssistantChatMessage {\n    role: \"assistant\";\n    content: MessageContent;\n    toolCalls?: ToolCallDelta[];\n  }\n  ```\n\n- **`SystemChatMessage`**: Define la estructura de un mensaje de sistema.\n  ```typescript\n  export interface SystemChatMessage {\n    role: \"system\";\n    content: string;\n  }\n  ```\n\n- **`ChatMessage`**: Define la estructura de un mensaje de chat.\n  ```typescript\n  export type ChatMessage =\n    | UserChatMessage\n    | AssistantChatMessage\n    | SystemChatMessage\n    | ToolResultChatMessage;\n  ```\n\n### Interfaces y Tipos para Elementos de Contexto\n- **`ContextItemId`**: Define la estructura de un identificador de elemento de contexto.\n  ```typescript\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n  ```\n\n- **`ContextItemUriTypes`**: Define los tipos posibles de URI de un elemento de contexto.\n  ```typescript\n  export type ContextItemUriTypes = \"file\" | \"url\";\n  ```\n\n- **`ContextItemUri`**: Define la estructura de una URI de un elemento de contexto.\n  ```typescript\n  export interface ContextItemUri {\n    type: ContextItemUriTypes;\n    value: string;\n  }\n  ```\n\n- **`ContextItem`**: Define la estructura de un elemento de contexto.\n  ```typescript\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n    uri?: ContextItemUri;\n    hidden?: boolean;\n  }\n  ```\n\n- **`ContextItemWithId`**: Define la estructura de un elemento de contexto con identificador.\n  ```typescript\n  export interface ContextItemWithId extends ContextItem {\n    id: ContextItemId;\n  }\n  ```\n\n### Interfaces y Tipos para Modificadores de Entrada\n- **`InputModifiers`**: Define la estructura de modificadores de entrada.\n  ```typescript\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n  ```\n\n### Interfaces y Tipos para Símbolos y Mapas de Archivos\n- **`SymbolWithRange`**: Define la estructura de un símbolo con rango.\n  ```typescript\n  export interface SymbolWithRange extends RangeInFile {\n    name: string;\n    type: Parser.SyntaxNode[\"type\"];\n    content: string;\n  }\n  ```\n\n- **`FileSymbolMap`**: Define la estructura de un mapa de símbolos de archivo.\n  ```typescript\n  export type FileSymbolMap = Record<string, SymbolWithRange[]>;\n  ```\n\n### Interfaces y Tipos para Registros de Prompt\n- **`PromptLog`**: Define la estructura de un registro de prompt.\n  ```typescript\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n  ```\n\n### Tipos y Enumeraciones para Modos de Mensaje y Estados de Herramienta\n- **`MessageModes`**: Define los modos posibles de mensaje.\n  ```typescript\n  type MessageModes = \"chat\" | \"edit\";\n  ```\n\n- **`ToolStatus`**: Define los estados posibles de una herramienta.\n  ```typescript\n  export type ToolStatus =\n    | \"generating\"\n    | \"generated\"\n    | \"calling\"\n    | \"done\"\n    | \"errored\"\n    | \"canceled\";\n  ```\n\n### Interfaces y Tipos para Estados de Herramienta\n- **`ToolCallState`**: Define la estructura del estado de una llamada a una herramienta.\n  ```typescript\n  interface ToolCallState {\n    toolCallId: string;\n    toolCall: ToolCall;\n    status: ToolStatus;\n    parsedArgs: any;\n    output?: ContextItem[];\n  }\n  ```\n\n### Interfaces y Tipos para Historial de Chat\n- **`ChatHistoryItem`**: Define la estructura de un elemento de historial de chat.\n  ```typescript\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    contextItems: ContextItemWithId[];\n    editorState?: any;\n    modifiers?: InputModifiers;\n    promptLogs?: PromptLog[];\n    toolCallState?: ToolCallState;\n    isGatheringContext?: boolean;\n  }\n  ```\n\n### Interfaces y Tipos para Opciones de Completación Completa\n- **`LLMFullCompletionOptions`**: Define la estructura de las opciones de completación completa.\n  ```typescript\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n    model?: string;\n  }\n  ```\n\n### Tipos y Enumeraciones para Tipos de Toast\n- **`ToastType`**: Define los tipos posibles de toast.\n  ```typescript\n  export type ToastType = \"info\" | \"error\" | \"warning\";\n  ```\n\n### Interfaces y Tipos para Interacciones con Modelos de IA\n- **`LLMInteractionBase`**: Define la estructura base de una interacción con un modelo de IA.\n  ```typescript\n  export interface LLMInteractionBase {\n    interactionId: string;\n    timestamp: number;\n  }\n  ```\n\n- **`LLMInteractionStartChat`**: Define la estructura de una interacción de inicio de chat.\n  ```typescript\n  export interface LLMInteractionStartChat extends LLMInteractionBase {\n    kind: \"startChat\";\n    messages: ChatMessage[];\n    options: CompletionOptions;\n  }\n  ```\n\n- **`LLMInteractionStartComplete`**: Define la estructura de una interacción de inicio de completación.\n  ```typescript\n  export interface LLMInteractionStartComplete extends LLMInteractionBase {\n    kind: \"startComplete\";\n    prompt: string;\n    options: CompletionOptions;\n  }\n  ```\n\n- **`LLMInteractionStartFim`**: Define la estructura de una interacción de inicio de FIM.\n  ```typescript\n  export interface LLMInteractionStartFim extends LLMInteractionBase {\n    kind: \"startFim\";\n    prefix: string;\n    suffix: string;\n    options: CompletionOptions;\n  }\n  ```\n\n- **`LLMInteractionChunk`**: Define la estructura de un fragmento de interacción.\n  ```typescript\n  export interface LLMInteractionChunk extends LLMInteractionBase {\n    kind: \"chunk\";\n    chunk: string;\n  }\n  ```\n\n- **`LLMInteractionMessage`**: Define la estructura de un mensaje de interacción.\n  ```typescript\n  export interface LLMInteractionMessage","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
{"timestamp":"2025-06-04T00:01:07.310Z","userId":"","userAgent":"Visual Studio Code - Insiders/1.101.0-insider (Continue/1.1.42)","selectedProfileId":"local","eventName":"chatInteraction","schema":"0.2.0","prompt":"<system>\n<important_rules>\n  You are in chat mode.\n\n  If the user asks to make changes to files offer that they can use the Apply Button on the code block, or switch to Agent Mode to make the suggested updates automatically.\n  If needed consisely explain to the user they can switch to agent mode using the Mode Selector dropdown and provide no other details.\n\n  Always include the language and file name in the info string when you write code blocks.\n  If you are editing \"src/main.py\" for example, your code block should start with '```python src/main.py'\n\n  When addressing code modification requests, present a concise code snippet that\n  emphasizes only the necessary changes and uses abbreviated placeholders for\n  unmodified sections. For example:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  {{ modified code here }}\n\n  // ... existing code ...\n\n  {{ another modification }}\n\n  // ... rest of code ...\n  ```\n\n  In existing files, you should always restate the function or class that the snippet belongs to:\n\n  ```language /path/to/file\n  // ... existing code ...\n\n  function exampleFunction() {\n    // ... existing code ...\n\n    {{ modified code here }}\n\n    // ... rest of function ...\n  }\n\n  // ... rest of code ...\n  ```\n\n  Since users have access to their complete file, they prefer reading only the\n  relevant modifications. It's perfectly acceptable to omit unmodified portions\n  at the beginning, middle, or end of files using these \"lazy\" comments. Only\n  provide the complete file when explicitly requested. Include a concise explanation\n  of changes unless the user specifically asks for code only.\n\n</important_rules>\n\n<user>\nlee el fichero\n\n<assistant>\nNo se encontró el archivo src/main.py. ¿Hay algo más en lo que pueda ayudarte?\n\n<user>\n```config.json\n{\n  \"models\": [\n    {\n      \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"GPT-4o (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Llama3.1 70b (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"llama3.1-70b\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Codestral (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"codestral-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    },\n    {\n      \"title\": \"Grok Beta\",\n      \"model\": \"grok-beta\",\n      \"contextLength\": 128000,\n      \"apiKey\": \"************************************************************************************\",\n      \"provider\": \"xAI\"\n    },\n    {\n      \"apiKey\": \"XfeFh79plKmcdMWwx2kUVBDTMV9Og7ey\",\n      \"title\": \"Codestral (1)\",\n      \"model\": \"codestral-latest\",\n      \"provider\": \"mistral\"\n    }\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Tab Autocomplete\",\n    \"provider\": \"free-trial\",\n    \"model\": \"codestral-latest\"\n  },\n  \"customCommands\": [\n    {\n      \"name\": \"test\",\n      \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n      \"description\": \"Write unit tests for highlighted code\"\n    }\n  ],\n  \"contextProviders\": [\n    {\n      \"name\": \"code\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"docs\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"diff\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"terminal\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"problems\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"folder\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"codebase\",\n      \"params\": {}\n    }\n  ],\n  \"slashCommands\": [\n    {\n      \"name\": \"share\",\n      \"description\": \"Export the current chat session to markdown\"\n    },\n    {\n      \"name\": \"cmd\",\n      \"description\": \"Generate a shell command\"\n    },\n    {\n      \"name\": \"commit\",\n      \"description\": \"Generate a git commit message\"\n    }\n  ],\n  \"embeddingsProvider\": {\n    \"provider\": \"free-trial\"\n  },\n  \"reranker\": {\n    \"name\": \"free-trial\"\n  }\n}\n```\n\nconfig.json lee este\n\n<assistant>\nEl archivo `config.json` contiene la configuración para varios modelos de inteligencia artificial, incluyendo detalles sobre sus proveedores, claves API, y mensajes del sistema. También incluye configuraciones para comandos personalizados, proveedores de contexto, comandos de barra diagonal, proveedor de incrustaciones y un clasificador.\n\nSi necesitas modificar algún aspecto de esta configuración o agregar nuevos modelos, por favor indícalo.\n\n<user>\nexplicame le ts\n\n<assistant>\nEl archivo `config.json` es un archivo de configuración en formato JSON que contiene varias secciones y parámetros para configurar diferentes aspectos de una aplicación o sistema. A continuación, se explica cada sección y sus componentes:\n\n### Sección `models`\nEsta sección define una lista de modelos de inteligencia artificial que pueden ser utilizados. Cada modelo tiene los siguientes atributos:\n- `title`: El nombre del modelo.\n- `provider`: El proveedor del modelo (puede ser un proveedor de prueba gratuita o un proveedor específico).\n- `model`: El identificador del modelo.\n- `systemMessage`: Un mensaje del sistema que describe el propósito del modelo.\n- `apiKey`: (opcional) La clave API para acceder al modelo.\n- `contextLength`: (opcional) La longitud del contexto que el modelo puede manejar.\n\nEjemplo:\n```json\n{\n  \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n  \"provider\": \"free-trial\",\n  \"model\": \"claude-3-5-sonnet-latest\",\n  \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n}\n```\n\n### Sección `tabAutocompleteModel`\nDefine el modelo utilizado para la autocompletación de pestañas:\n- `title`: El nombre del modelo.\n- `provider`: El proveedor del modelo.\n- `model`: El identificador del modelo.\n\nEjemplo:\n```json\n{\n  \"title\": \"Tab Autocomplete\",\n  \"provider\": \"free-trial\",\n  \"model\": \"codestral-latest\"\n}\n```\n\n### Sección `customCommands`\nDefine una lista de comandos personalizados que pueden ser utilizados en la aplicación:\n- `name`: El nombre del comando.\n- `prompt`: El prompt asociado al comando.\n- `description`: Una descripción del comando.\n\nEjemplo:\n```json\n{\n  \"name\": \"test\",\n  \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n  \"description\": \"Write unit tests for highlighted code\"\n}\n```\n\n### Sección `contextProviders`\nDefine una lista de proveedores de contexto que pueden ser utilizados:\n- `name`: El nombre del proveedor de contexto.\n- `params`: Parámetros adicionales para el proveedor de contexto.\n\nEjemplo:\n```json\n{\n  \"name\": \"code\",\n  \"params\": {}\n}\n```\n\n### Sección `slashCommands`\nDefine una lista de comandos de barra diagonal que pueden ser utilizados:\n- `name`: El nombre del comando.\n- `description`: Una descripción del comando.\n\nEjemplo:\n```json\n{\n  \"name\": \"share\",\n  \"description\": \"Export the current chat session to markdown\"\n}\n```\n\n### Sección `embeddingsProvider`\nDefine el proveedor de incrustaciones:\n- `provider`: El nombre del proveedor.\n\nEjemplo:\n```json\n{\n  \"provider\": \"free-trial\"\n}\n```\n\n### Sección `reranker`\nDefine el clasificador:\n- `name`: El nombre del clasificador.\n\nEjemplo:\n```json\n{\n  \"name\": \"free-trial\"\n}\n```\n\n### Resumen\nEl archivo `config.json` es una configuración centralizada que permite definir y gestionar múltiples aspectos de una aplicación, incluyendo modelos de IA, comandos personalizados, proveedores de contexto, comandos de barra diagonal, proveedores de incrustaciones y clasificadores. Cada sección está diseñada para ser modular y fácil de extender o modificar según sea necesario.\n\n<user>\n```types/core/index.d.ts\n\ndeclare global {\n  import Parser from \"web-tree-sitter\";\n  import { GetGhTokenArgs } from \"./protocol/ide\";\n  declare global {\n    interface Window {\n      ide?: \"vscode\";\n      windowId: string;\n      serverUrl: string;\n      vscMachineId: string;\n      vscMediaUrl: string;\n      fullColorTheme?: {\n        rules?: {\n          token?: string;\n          foreground?: string;\n        }[];\n      };\n      colorThemeName?: string;\n      workspacePaths?: string[];\n      postIntellijMessage?: (\n        messageType: string,\n        data: any,\n        messageIde: string,\n      ) => void;\n    }\n  }\n  \n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n  \n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n  \n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status:\n      | \"loading\"\n      | \"indexing\"\n      | \"done\"\n      | \"failed\"\n      | \"paused\"\n      | \"disabled\"\n      | \"cancelled\";\n    debugInfo?: string;\n  }\n  \n  // This is more or less a V2 of IndexingProgressUpdate for docs etc.\n  export interface IndexingStatus {\n    id: string;\n    type: \"docs\";\n    progress: number;\n    description: string;\n    status: \"indexing\" | \"complete\" | \"paused\" | \"failed\" | \"aborted\" | \"pending\";\n    embeddingsProviderId: string;\n    isReindexing?: boolean;\n    debugInfo?: string;\n    title: string;\n    icon?: string;\n    url?: string;\n  }\n  \n  export type PromptTemplateFunction = (\n    history: ChatMessage[],\n    otherData: Record<string, string>,\n  ) => string | ChatMessage[];\n  \n  export type PromptTemplate = string | PromptTemplateFunction;\n  \n  export interface ILLM extends LLMOptions {\n    get providerName(): string;\n  \n    uniqueId: string;\n    model: string;\n  \n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    llmLogger?: ILLMLogger;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n    cacheBehavior?: CacheBehavior;\n  \n    deployment?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n  \n    // Embedding options\n    embeddingId: string;\n    maxEmbeddingChunkSize: number;\n    maxEmbeddingBatchSize: number;\n  \n    complete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<string>;\n  \n    streamComplete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n  \n    streamFim(\n      prefix: string,\n      suffix: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n  \n    streamChat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n  \n    chat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n  \n    embed(chunks: string[]): Promise<number[][]>;\n  \n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  \n    countTokens(text: string): number;\n  \n    supportsImages(): boolean;\n  \n    supportsCompletions(): boolean;\n  \n    supportsPrefill(): boolean;\n  \n    supportsFim(): boolean;\n  \n    listModels(): Promise<string[]>;\n  \n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n  \n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n  \n  export interface ContextProviderDescription {\n    title: ContextProviderName;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n    dependsOnIndexing?: boolean;\n  }\n  \n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n  \n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: ILLM;\n    reranker: ILLM | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n  \n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n  \n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n  \n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n  \n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n  \n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n  \n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n  \n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n  \n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n  \n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n  \n  export interface Session {\n    sessionId: string;\n    title: string;\n    workspaceDirectory: string;\n    history: ChatHistoryItem[];\n  }\n  \n  export interface SessionMetadata {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n  \n  export interface RangeInFile {\n    filepath: string;\n    range: Range;\n  }\n  \n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n  \n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n  \n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  \n  export interface Position {\n    line: number;\n    character: number;\n  }\n  \n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n  \n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n  \n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n  \n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\" | \"tool\";\n  \n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n  \n  export type MessageContent = string | MessagePart[];\n  \n  export interface ToolCall {\n    id: string;\n    type: \"function\";\n    function: {\n      name: string;\n      arguments: string;\n    };\n  }\n  \n  export interface ToolCallDelta {\n    id?: string;\n    type?: \"function\";\n    function?: {\n      name?: string;\n      arguments?: string;\n    };\n  }\n  \n  export interface ToolResultChatMessage {\n    role: \"tool\";\n    content: string;\n    toolCallId: string;\n  }\n  \n  export interface UserChatMessage {\n    role: \"user\";\n    content: MessageContent;\n  }\n  \n  export interface AssistantChatMessage {\n    role: \"assistant\";\n    content: MessageContent;\n    toolCalls?: ToolCallDelta[];\n  }\n  \n  export interface SystemChatMessage {\n    role: \"system\";\n    content: string;\n  }\n  \n  export type ChatMessage =\n    | UserChatMessage\n    | AssistantChatMessage\n    | SystemChatMessage\n    | ToolResultChatMessage;\n  \n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n  \n  export type ContextItemUriTypes = \"file\" | \"url\";\n  \n  export interface ContextItemUri {\n    type: ContextItemUriTypes;\n    value: string;\n  }\n  \n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n    uri?: ContextItemUri;\n    hidden?: boolean;\n  }\n  \n  export interface ContextItemWithId extends ContextItem {\n    id: ContextItemId;\n  }\n  \n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n  \n  export interface SymbolWithRange extends RangeInFile {\n    name: string;\n    type: Parser.SyntaxNode[\"type\"];\n    content: string;\n  }\n  \n  export type FileSymbolMap = Record<string, SymbolWithRange[]>;\n  \n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n  \n  type MessageModes = \"chat\" | \"edit\";\n  \n  export type ToolStatus =\n    | \"generating\"\n    | \"generated\"\n    | \"calling\"\n    | \"done\"\n    | \"errored\"\n    | \"canceled\";\n  \n  // Will exist only on \"assistant\" messages with tool calls\n  interface ToolCallState {\n    toolCallId: string;\n    toolCall: ToolCall;\n    status: ToolStatus;\n    parsedArgs: any;\n    output?: ContextItem[];\n  }\n  \n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    contextItems: ContextItemWithId[];\n    editorState?: any;\n    modifiers?: InputModifiers;\n    promptLogs?: PromptLog[];\n    toolCallState?: ToolCallState;\n    isGatheringContext?: boolean;\n  }\n  \n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n    model?: string;\n  }\n  \n  export type ToastType = \"info\" | \"error\" | \"warning\";\n  \n  export interface LLMInteractionBase {\n    interactionId: string;\n    timestamp: number;\n  }\n  \n  export interface LLMInteractionStartChat extends LLMInteractionBase {\n    kind: \"startChat\";\n    messages: ChatMessage[];\n    options: CompletionOptions;\n  }\n  \n  export interface LLMInteractionStartComplete extends LLMInteractionBase {\n    kind: \"startComplete\";\n    prompt: string;\n    options: CompletionOptions;\n  }\n  \n  export interface LLMInteractionStartFim extends LLMInteractionBase {\n    kind: \"startFim\";\n    prefix: string;\n    suffix: string;\n    options: CompletionOptions;\n  }\n  \n  export interface LLMInteractionChunk extends LLMInteractionBase {\n    kind: \"chunk\";\n    chunk: string;\n  }\n  \n  export interface LLMInteractionMessage extends LLMInteractionBase {\n    kind: \"message\";\n    message: ChatMessage;\n  }\n  \n  export interface LLMInteractionEnd extends LLMInteractionBase {\n    promptTokens: number;\n    generatedTokens: number;\n    thinkingTokens: number;\n  }\n  \n  export interface LLMInteractionSuccess extends LLMInteractionEnd {\n    kind: \"success\";\n  }\n  \n  export interface LLMInteractionCancel extends LLMInteractionEnd {\n    kind: \"cancel\";\n  }\n  \n  export interface LLMInteractionError extends LLMInteractionEnd {\n    kind: \"error\";\n    name: string;\n    message: string;\n  }\n  \n  export type LLMInteractionItem =\n    | LLMInteractionStartChat\n    | LLMInteractionStartComplete\n    | LLMInteractionStartFim\n    | LLMInteractionChunk\n    | LLMInteractionMessage\n    | LLMInteractionSuccess\n    | LLMInteractionCancel\n    | LLMInteractionError;\n  \n  // When we log a LLM interaction, we want to add the interactionId and timestamp\n  // in the logger code, so we need a type that omits these members from *each*\n  // member of the union. This can be done by using the distributive behavior of\n  // conditional types in Typescript.\n  //\n  // www.typescriptlang.org/docs/handbook/2/conditional-types.html#distributive-conditional-types\n  // https://stackoverflow.com/questions/57103834/typescript-omit-a-property-from-all-interfaces-in-a-union-but-keep-the-union-s\n  type DistributiveOmit<T, K extends PropertyKey> = T extends unknown\n    ? Omit<T, K>\n    : never;\n  \n  export type LLMInteractionItemDetails = DistributiveOmit<\n    LLMInteractionItem,\n    \"interactionId\" | \"timestamp\"\n  >;\n  \n  export interface ILLMInteractionLog {\n    logItem(item: LLMInteractionItemDetails): void;\n  }\n  \n  export interface ILLMLogger {\n    createInteractionLog(): ILLMInteractionLog;\n  }\n  \n  export interface LLMOptions {\n    model: string;\n  \n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    logger?: ILLMLogger;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n    cacheBehavior?: CacheBehavior;\n  \n    useLegacyCompletionsEndpoint?: boolean;\n  \n    // Embedding options\n    embeddingId?: string;\n    maxEmbeddingChunkSize?: number;\n    maxEmbeddingBatchSize?: number;\n  \n    // Cloudflare options\n    accountId?: string;\n  \n    // Azure options\n    deployment?: string;\n    apiVersion?: string;\n    apiType?: string;\n  \n    // AWS options\n    profile?: string;\n    modelArn?: string;\n  \n    // AWS and GCP Options\n    region?: string;\n  \n    // GCP Options\n    capabilities?: ModelCapability;\n  \n    // GCP and Watsonx Options\n    projectId?: string;\n  \n    // IBM watsonx Options\n    deploymentId?: string;\n  }\n  \n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n  \n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      signal: AbortSignal,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n  \n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n  \n  // IDE\n  \n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n  \n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n  \n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n  \n  export class Thread {\n    name: string;\n    id: number;\n  }\n  \n  export type IdeType = \"vscode\" | \"jetbrains\";\n  \n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n  \n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n  \n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n  \n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n  \n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    pauseCodebaseIndexOnStart: boolean;\n  }\n  \n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n  \n    getIdeSettings(): Promise<IdeSettings>;\n  \n    getDiff(includeUnstaged: boolean): Promise<string[]>;\n  \n    getClipboardContent(): Promise<{ text: string; copiedAt: string }>;\n  \n    isTelemetryEnabled(): Promise<boolean>;\n  \n    getUniqueId(): Promise<string>;\n  \n    getTerminalContents(): Promise<string>;\n  \n    getDebugLocals(threadIndex: number): Promise<string>;\n  \n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n  \n    getAvailableThreads(): Promise<Thread[]>;\n  \n    getWorkspaceDirs(): Promise<string[]>;\n  \n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n  \n    fileExists(filepath: string): Promise<boolean>;\n  \n    writeFile(path: string, contents: string): Promise<void>;\n  \n    showVirtualFile(title: string, contents: string): Promise<void>;\n    openFile(path: string): Promise<void>;\n  \n    openUrl(url: string): Promise<void>;\n  \n    runCommand(command: string): Promise<void>;\n  \n    saveFile(filepath: string): Promise<void>;\n  \n    readFile(filepath: string): Promise<string>;\n  \n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n  \n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n  \n    getCurrentFile(): Promise<\n      | undefined\n      | {\n          isUntitled: boolean;\n          path: string;\n          contents: string;\n        }\n    >;\n  \n    getPinnedFiles(): Promise<string[]>;\n  \n    getSearchResults(query: string): Promise<string>;\n  \n    subprocess(command: string, cwd?: string): Promise<[string, string]>;\n  \n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n  \n    getBranch(dir: string): Promise<string>;\n  \n    getTags(artifactId: string): Promise<IndexTag[]>;\n  \n    getRepoName(dir: string): Promise<string | undefined>;\n  \n    showToast(\n      type: ToastType,\n      message: string,\n      ...otherParams: any[]\n    ): Promise<any>;\n  \n    getGitRootPath(dir: string): Promise<string | undefined>;\n  \n    listDir(dir: string): Promise<[string, FileType][]>;\n  \n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n  \n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n  \n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n  }\n  \n  // Slash Commands\n  \n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n  \n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n  \n  // Config\n  \n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n  \n  type ContextProviderName =\n    | \"diff\"\n    | \"terminal\"\n    | \"debugger\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\"\n    | \"greptile\"\n    | \"outline\"\n    | \"continue-proxy\"\n    | \"highlights\"\n    | \"file\"\n    | \"issue\"\n    | \"repo-map\"\n    | \"url\"\n    | string;\n  \n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"granite\"\n    | \"llama3\"\n    | \"codestral\";\n  \n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n  \n  export interface CacheBehavior {\n    cacheSystemMessage?: boolean;\n    cacheConversation?: boolean;\n  }\n  \n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n  \n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n  \n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n  \n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n  \n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n  \n  interface Prediction {\n    type: \"content\";\n    content:\n      | string\n      | {\n          type: \"text\";\n          text: string;\n        }[];\n  }\n  \n  export interface ToolExtras {\n    ide: IDE;\n    llm: ILLM;\n    fetch: FetchFunction;\n  }\n  \n  export interface Tool {\n    type: \"function\";\n    function: {\n      name: string;\n      description?: string;\n      parameters?: Record<string, any>;\n      strict?: boolean | null;\n    };\n  \n    displayTitle: string;\n    wouldLikeTo: string;\n    readonly: boolean;\n    uri?: string;\n  }\n  \n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    useMmap?: boolean;\n    keepAlive?: number;\n    numGpu?: number;\n    raw?: boolean;\n    stream?: boolean;\n    prediction?: Prediction;\n    tools?: Tool[];\n  }\n  \n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n  \n  export interface ModelDescription {\n    title: string;\n    provider: string;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n    cacheBehavior?: CacheBehavior;\n  }\n  \n  export interface JSONEmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    deployment?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n    maxBatchSize?: number;\n    // AWS options\n    profile?: string;\n  \n    // AWS and GCP Options\n    region?: string;\n  \n    // GCP and Watsonx Options\n    projectId?: string;\n  }\n  \n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: string;\n  }\n  \n  export interface RerankerDescription {\n    name: string;\n    params?: { [key: string]: any };\n  }\n  \n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    transform?: boolean;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useRecentlyEdited: boolean;\n    disableInFiles?: string[];\n    useImports?: boolean;\n    showWhateverWeHaveAtXMs?: number;\n  }\n  \n  interface StdioOptions {\n    type: \"stdio\";\n    command: string;\n    args: string[];\n  }\n  \n  interface WebSocketOptions {\n    type: \"websocket\";\n    url: string;\n  }\n  \n  interface SSEOptions {\n    type: \"sse\";\n    url: string;\n  }\n  \n  type TransportOptions = StdioOptions | WebSocketOptions | SSEOptions;\n  \n  export interface MCPOptions {\n    transport: TransportOptions;\n  }\n  \n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n    showChatScrollbar?: boolean;\n    codeWrap?: boolean;\n  }\n  \n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n  \n  interface ExperimentalModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n    repoMapFileSelection?: string;\n  }\n  \n  export type ApplyStateStatus =\n    | \"streaming\" // Changes are being applied to the file\n    | \"done\" // All changes have been applied, awaiting user to accept/reject\n    | \"closed\"; // All changes have been applied. Note that for new files, we immediately set the status to \"closed\"\n  \n  export interface ApplyState {\n    streamId: string;\n    status?: ApplyStateStatus;\n    numDiffs?: number;\n    filepath?: string;\n    fileContent?: string;\n  }\n  \n  export interface RangeInFileWithContents {\n    filepath: string;\n    range: {\n      start: { line: number; character: number };\n      end: { line: number; character: number };\n    };\n    contents: string;\n  }\n  \n  export type CodeToEdit = RangeInFileWithContents | FileWithContents;\n  \n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n  \n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n  \n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n  \n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n  \n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ExperimentalModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n  \n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n  \n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  \n    /**\n     * If set to true, we will attempt to pull down and install an instance of Chromium\n     * that is compatible with the current version of Puppeteer.\n     * This is needed to crawl a large number of documentation sites that are dynamically rendered.\n     */\n    useChromiumForDocsCrawling?: boolean;\n    useTools?: boolean;\n    modelContextProtocolServers?: MCPOptions[];\n  }\n  \n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n  \n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n  \n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n  \n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n  \n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | ILLM;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | ILLM;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n  \n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: ILLM;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: ILLM;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n    tools: Tool[];\n  }\n  \n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n    tools: Tool[];\n  }\n  \n  // DOCS SUGGESTIONS AND PACKAGE INFO\n  export interface FilePathAndName {\n    path: string;\n    name: string;\n  }\n  \n  export interface PackageFilePathAndName extends FilePathAndName {\n    packageRegistry: string; // e.g. npm, pypi\n  }\n  \n  export type ParsedPackageInfo = {\n    name: string;\n    packageFile: PackageFilePathAndName;\n    language: string;\n    version: string;\n  };\n  \n  export type PackageDetails = {\n    docsLink?: string;\n    docsLinkWarning?: string;\n    title?: string;\n    description?: string;\n    repo?: string;\n    license?: string;\n  };\n  \n  export type PackageDetailsSuccess = PackageDetails & {\n    docsLink: string;\n  };\n  \n  export type PackageDocsResult = {\n    packageInfo: ParsedPackageInfo;\n  } & (\n    | { error: string; details?: never }\n    | { details: PackageDetailsSuccess; error?: never }\n  );\n}\n\nexport {};\n\n```\n\nindex.d.ts ahora este\n\n<assistant>\nEl archivo `index.d.ts` es un archivo de definiciones de tipos para TypeScript. Define interfaces, tipos y declaraciones globales que pueden ser utilizadas en un proyecto TypeScript. A continuación, se explica cada sección y sus componentes:\n\n### Sección `global`\nEsta sección define declaraciones globales que pueden ser utilizadas en cualquier parte del proyecto.\n\n#### Interfaces y Tipos Globales\n- **`Window`**: Define propiedades adicionales que se pueden agregar al objeto `window` en el navegador.\n  ```typescript\n  interface Window {\n    ide?: \"vscode\";\n    windowId: string;\n    serverUrl: string;\n    vscMachineId: string;\n    vscMediaUrl: string;\n    fullColorTheme?: {\n      rules?: {\n        token?: string;\n        foreground?: string;\n      }[];\n    };\n    colorThemeName?: string;\n    workspacePaths?: string[];\n    postIntellijMessage?: (\n      messageType: string,\n      data: any,\n      messageIde: string,\n    ) => void;\n  }\n  ```\n\n- **`ChunkWithoutID`**: Define la estructura de un fragmento de texto sin un identificador.\n  ```typescript\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n  ```\n\n- **`Chunk`**: Extiende `ChunkWithoutID` añadiendo propiedades adicionales.\n  ```typescript\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n  ```\n\n- **`IndexingProgressUpdate`**: Define la estructura de una actualización de progreso de indexación.\n  ```typescript\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status:\n      | \"loading\"\n      | \"indexing\"\n      | \"done\"\n      | \"failed\"\n      | \"paused\"\n      | \"disabled\"\n      | \"cancelled\";\n    debugInfo?: string;\n  }\n  ```\n\n- **`IndexingStatus`**: Define la estructura del estado de indexación.\n  ```typescript\n  export interface IndexingStatus {\n    id: string;\n    type: \"docs\";\n    progress: number;\n    description: string;\n    status: \"indexing\" | \"complete\" | \"paused\" | \"failed\" | \"aborted\" | \"pending\";\n    embeddingsProviderId: string;\n    isReindexing?: boolean;\n    debugInfo?: string;\n    title: string;\n    icon?: string;\n    url?: string;\n  }\n  ```\n\n- **`PromptTemplateFunction`**: Define una función que genera una plantilla de prompt.\n  ```typescript\n  export type PromptTemplateFunction = (\n    history: ChatMessage[],\n    otherData: Record<string, string>,\n  ) => string | ChatMessage[];\n  ```\n\n- **`PromptTemplate`**: Define una plantilla de prompt que puede ser una cadena o una función.\n  ```typescript\n  export type PromptTemplate = string | PromptTemplateFunction;\n  ```\n\n### Interfaces y Tipos para Modelos de IA\n- **`ILLM`**: Define la estructura de un modelo de lenguaje de inteligencia artificial.\n  ```typescript\n  export interface ILLM extends LLMOptions {\n    get providerName(): string;\n    uniqueId: string;\n    model: string;\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    llmLogger?: ILLMLogger;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n    cacheBehavior?: CacheBehavior;\n    deployment?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n    embeddingId: string;\n    maxEmbeddingChunkSize: number;\n    maxEmbeddingBatchSize: number;\n    complete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<string>;\n    streamComplete(\n      prompt: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n    streamFim(\n      prefix: string,\n      suffix: string,\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n    streamChat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n    chat(\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n    embed(chunks: string[]): Promise<number[][]>;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n    countTokens(text: string): number;\n    supportsImages(): boolean;\n    supportsCompletions(): boolean;\n    supportsPrefill(): boolean;\n    supportsFim(): boolean;\n    listModels(): Promise<string[]>;\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n  ```\n\n### Interfaces y Tipos para Proveedores de Contexto\n- **`ContextProviderType`**: Define los tipos de proveedores de contexto.\n  ```typescript\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n  ```\n\n- **`ContextProviderDescription`**: Define la estructura de una descripción de proveedor de contexto.\n  ```typescript\n  export interface ContextProviderDescription {\n    title: ContextProviderName;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n    dependsOnIndexing?: boolean;\n  }\n  ```\n\n- **`FetchFunction`**: Define una función de tipo `fetch`.\n  ```typescript\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n  ```\n\n- **`ContextProviderExtras`**: Define las propiedades adicionales que pueden ser pasadas a un proveedor de contexto.\n  ```typescript\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: ILLM;\n    reranker: ILLM | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n  ```\n\n- **`LoadSubmenuItemsArgs`**: Define los argumentos para cargar elementos de submenú.\n  ```typescript\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n  ```\n\n- **`CustomContextProvider`**: Define la estructura de un proveedor de contexto personalizado.\n  ```typescript\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n  ```\n\n### Interfaces y Tipos para Configuración de Indexación de Sitios\n- **`SiteIndexingConfig`**: Define la estructura de la configuración de indexación de sitios.\n  ```typescript\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n  ```\n\n### Interfaces y Tipos para Proveedores de Contexto\n- **`IContextProvider`**: Define la estructura de un proveedor de contexto.\n  ```typescript\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n  ```\n\n### Interfaces y Tipos para Sesiones\n- **`Session`**: Define la estructura de una sesión.\n  ```typescript\n  export interface Session {\n    sessionId: string;\n    title: string;\n    workspaceDirectory: string;\n    history: ChatHistoryItem[];\n  }\n  ```\n\n- **`SessionMetadata`**: Define la estructura de los metadatos de una sesión.\n  ```typescript\n  export interface SessionMetadata {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n  ```\n\n### Interfaces y Tipos para Rangos y Posiciones en Archivos\n- **`RangeInFile`**: Define la estructura de un rango en un archivo.\n  ```typescript\n  export interface RangeInFile {\n    filepath: string;\n    range: Range;\n  }\n  ```\n\n- **`Location`**: Define la estructura de una ubicación en un archivo.\n  ```typescript\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n  ```\n\n- **`FileWithContents`**: Define la estructura de un archivo con su contenido.\n  ```typescript\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n  ```\n\n- **`Range`**: Define la estructura de un rango.\n  ```typescript\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  ```\n\n- **`Position`**: Define la estructura de una posición.\n  ```typescript\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  ```\n\n### Interfaces y Tipos para Ediciones de Archivos\n- **`FileEdit`**: Define la estructura de una edición de archivo.\n  ```typescript\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n  ```\n\n### Interfaces y Tipos para Errores\n- **`ContinueError`**: Define la estructura de un error.\n  ```typescript\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n  ```\n\n### Interfaces y Tipos para Opciones de Completación\n- **`CompletionOptions`**: Define la estructura de las opciones de completación.\n  ```typescript\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n  ```\n\n### Interfaces y Tipos para Mensajes de Chat\n- **`ChatMessageRole`**: Define los roles posibles de un mensaje de chat.\n  ```typescript\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\" | \"tool\";\n  ```\n\n- **`MessagePart`**: Define la estructura de una parte de un mensaje.\n  ```typescript\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n  ```\n\n- **`MessageContent`**: Define el contenido de un mensaje.\n  ```typescript\n  export type MessageContent = string | MessagePart[];\n  ```\n\n- **`ToolCall`**: Define la estructura de una llamada a una herramienta.\n  ```typescript\n  export interface ToolCall {\n    id: string;\n    type: \"function\";\n    function: {\n      name: string;\n      arguments: string;\n    };\n  }\n  ```\n\n- **`ToolCallDelta`**: Define la estructura de un delta de llamada a una herramienta.\n  ```typescript\n  export interface ToolCallDelta {\n    id?: string;\n    type?: \"function\";\n    function?: {\n      name?: string;\n      arguments?: string;\n    };\n  }\n  ```\n\n- **`ToolResultChatMessage`**: Define la estructura de un mensaje de resultado de herramienta.\n  ```typescript\n  export interface ToolResultChatMessage {\n    role: \"tool\";\n    content: string;\n    toolCallId: string;\n  }\n  ```\n\n- **`UserChatMessage`**: Define la estructura de un mensaje de usuario.\n  ```typescript\n  export interface UserChatMessage {\n    role: \"user\";\n    content: MessageContent;\n  }\n  ```\n\n- **`AssistantChatMessage`**: Define la estructura de un mensaje de asistente.\n  ```typescript\n  export interface AssistantChatMessage {\n    role: \"assistant\";\n    content: MessageContent;\n    toolCalls?: ToolCallDelta[];\n  }\n  ```\n\n- **`SystemChatMessage`**: Define la estructura de un mensaje de sistema.\n  ```typescript\n  export interface SystemChatMessage {\n    role: \"system\";\n    content: string;\n  }\n  ```\n\n- **`ChatMessage`**: Define la estructura de un mensaje de chat.\n  ```typescript\n  export type ChatMessage =\n    | UserChatMessage\n    | AssistantChatMessage\n    | SystemChatMessage\n    | ToolResultChatMessage;\n  ```\n\n### Interfaces y Tipos para Elementos de Contexto\n- **`ContextItemId`**: Define la estructura de un identificador de elemento de contexto.\n  ```typescript\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n  ```\n\n- **`ContextItemUriTypes`**: Define los tipos posibles de URI de un elemento de contexto.\n  ```typescript\n  export type ContextItemUriTypes = \"file\" | \"url\";\n  ```\n\n- **`ContextItemUri`**: Define la estructura de una URI de un elemento de contexto.\n  ```typescript\n  export interface ContextItemUri {\n    type: ContextItemUriTypes;\n    value: string;\n  }\n  ```\n\n- **`ContextItem`**: Define la estructura de un elemento de contexto.\n  ```typescript\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n    uri?: ContextItemUri;\n    hidden?: boolean;\n  }\n  ```\n\n- **`ContextItemWithId`**: Define la estructura de un elemento de contexto con identificador.\n  ```typescript\n  export interface ContextItemWithId extends ContextItem {\n    id: ContextItemId;\n  }\n  ```\n\n### Interfaces y Tipos para Modificadores de Entrada\n- **`InputModifiers`**: Define la estructura de modificadores de entrada.\n  ```typescript\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n  ```\n\n### Interfaces y Tipos para Símbolos y Mapas de Archivos\n- **`SymbolWithRange`**: Define la estructura de un símbolo con rango.\n  ```typescript\n  export interface SymbolWithRange extends RangeInFile {\n    name: string;\n    type: Parser.SyntaxNode[\"type\"];\n    content: string;\n  }\n  ```\n\n- **`FileSymbolMap`**: Define la estructura de un mapa de símbolos de archivo.\n  ```typescript\n  export type FileSymbolMap = Record<string, SymbolWithRange[]>;\n  ```\n\n### Interfaces y Tipos para Registros de Prompt\n- **`PromptLog`**: Define la estructura de un registro de prompt.\n  ```typescript\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n  ```\n\n### Tipos y Enumeraciones para Modos de Mensaje y Estados de Herramienta\n- **`MessageModes`**: Define los modos posibles de mensaje.\n  ```typescript\n  type MessageModes = \"chat\" | \"edit\";\n  ```\n\n- **`ToolStatus`**: Define los estados posibles de una herramienta.\n  ```typescript\n  export type ToolStatus =\n    | \"generating\"\n    | \"generated\"\n    | \"calling\"\n    | \"done\"\n    | \"errored\"\n    | \"canceled\";\n  ```\n\n### Interfaces y Tipos para Estados de Herramienta\n- **`ToolCallState`**: Define la estructura del estado de una llamada a una herramienta.\n  ```typescript\n  interface ToolCallState {\n    toolCallId: string;\n    toolCall: ToolCall;\n    status: ToolStatus;\n    parsedArgs: any;\n    output?: ContextItem[];\n  }\n  ```\n\n### Interfaces y Tipos para Historial de Chat\n- **`ChatHistoryItem`**: Define la estructura de un elemento de historial de chat.\n  ```typescript\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    contextItems: ContextItemWithId[];\n    editorState?: any;\n    modifiers?: InputModifiers;\n    promptLogs?: PromptLog[];\n    toolCallState?: ToolCallState;\n    isGatheringContext?: boolean;\n  }\n  ```\n\n### Interfaces y Tipos para Opciones de Completación Completa\n- **`LLMFullCompletionOptions`**: Define la estructura de las opciones de completación completa.\n  ```typescript\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n    model?: string;\n  }\n  ```\n\n### Tipos y Enumeraciones para Tipos de Toast\n- **`ToastType`**: Define los tipos posibles de toast.\n  ```typescript\n  export type ToastType = \"info\" | \"error\" | \"warning\";\n  ```\n\n### Interfaces y Tipos para Interacciones con Modelos de IA\n- **`LLMInteractionBase`**: Define la estructura base de una interacción con un modelo de IA.\n  ```typescript\n  export interface LLMInteractionBase {\n    interactionId: string;\n    timestamp: number;\n  }\n  ```\n\n- **`LLMInteractionStartChat`**: Define la estructura de una interacción de inicio de chat.\n  ```typescript\n  export interface LLMInteractionStartChat extends LLMInteractionBase {\n    kind: \"startChat\";\n    messages: ChatMessage[];\n    options: CompletionOptions;\n  }\n  ```\n\n- **`LLMInteractionStartComplete`**: Define la estructura de una interacción de inicio de completación.\n  ```typescript\n  export interface LLMInteractionStartComplete extends LLMInteractionBase {\n    kind: \"startComplete\";\n    prompt: string;\n    options: CompletionOptions;\n  }\n  ```\n\n- **`LLMInteractionStartFim`**: Define la estructura de una interacción de inicio de FIM.\n  ```typescript\n  export interface LLMInteractionStartFim extends LLMInteractionBase {\n    kind: \"startFim\";\n    prefix: string;\n    suffix: string;\n    options: CompletionOptions;\n  }\n  ```\n\n- **`LLMInteractionChunk`**: Define la estructura de un fragmento de interacción.\n  ```typescript\n  export interface LLMInteractionChunk extends LLMInteractionBase {\n    kind: \"chunk\";\n    chunk: string;\n  }\n  ```\n\n- **`LLMInteractionMessage`**: Define la estructura de un mensaje de interacción.\n  ```typescript\n  export interface LLMInteractionMessage\n\n<user>\ncontinua\n\n","completion":"```typescript\n  export interface LLMInteractionMessage extends LLMInteractionBase {\n    kind: \"message\";\n    message: ChatMessage;\n  }\n  ```\n\n- **`LLMInteractionEnd`**: Define la estructura de una interacción de finalización.\n  ```typescript\n  export interface LLMInteractionEnd extends LLMInteractionBase {\n    promptTokens: number;\n    generatedTokens: number;\n    thinkingTokens: number;\n  }\n  ```\n\n- **`LLMInteractionSuccess`**: Define la estructura de una interacción exitosa.\n  ```typescript\n  export interface LLMInteractionSuccess extends LLMInteractionEnd {\n    kind: \"success\";\n  }\n  ```\n\n- **`LLMInteractionCancel`**: Define la estructura de una interacción cancelada.\n  ```typescript\n  export interface LLMInteractionCancel extends LLMInteractionEnd {\n    kind: \"cancel\";\n  }\n  ```\n\n- **`LLMInteractionError`**: Define la estructura de una interacción con error.\n  ```typescript\n  export interface LLMInteractionError extends LLMInteractionEnd {\n    kind: \"error\";\n    name: string;\n    message: string;\n  }\n  ```\n\n- **`LLMInteractionItem`**: Define la estructura de un elemento de interacción.\n  ```typescript\n  export type LLMInteractionItem =\n    | LLMInteractionStartChat\n    | LLMInteractionStartComplete\n    | LLMInteractionStartFim\n    | LLMInteractionChunk\n    | LLMInteractionMessage\n    | LLMInteractionSuccess\n    | LLMInteractionCancel\n    | LLMInteractionError;\n  ```\n\n- **`DistributiveOmit`**: Define un tipo que omite propiedades de una unión de tipos.\n  ```typescript\n  type DistributiveOmit<T, K extends PropertyKey> = T extends unknown\n    ? Omit<T, K>\n    : never;\n  ```\n\n- **`LLMInteractionItemDetails`**: Define la estructura de los detalles de un elemento de interacción.\n  ```typescript\n  export type LLMInteractionItemDetails = DistributiveOmit<\n    LLMInteractionItem,\n    \"interactionId\" | \"timestamp\"\n  >;\n  ```\n\n### Interfaces y Tipos para Registro de Interacciones\n- **`ILLMInteractionLog`**: Define la estructura de un registro de interacción.\n  ```typescript\n  export interface ILLMInteractionLog {\n    logItem(item: LLMInteractionItemDetails): void;\n  }\n  ```\n\n- **`ILLMLogger`**: Define la estructura de un registrador de interacciones.\n  ```typescript\n  export interface ILLMLogger {\n    createInteractionLog(): ILLMInteractionLog;\n  }\n  ```\n\n### Interfaces y Tipos para Opciones de Modelos de IA\n- **`LLMOptions`**: Define la estructura de las opciones de un modelo de IA.\n  ```typescript\n  export interface LLMOptions {\n    model: string;\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    logger?: ILLMLogger;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n    cacheBehavior?: CacheBehavior;\n    useLegacyCompletionsEndpoint?: boolean;\n    embeddingId?: string;\n    maxEmbeddingChunkSize?: number;\n    maxEmbeddingBatchSize?: number;\n    accountId?: string;\n    deployment?: string;\n    apiVersion?: string;\n    apiType?: string;\n    profile?: string;\n    modelArn?: string;\n    region?: string;\n    capabilities?: ModelCapability;\n    projectId?: string;\n    deploymentId?: string;\n  }\n  ```\n\n### Tipos y Enumeraciones para Modelos de IA Personalizados\n- **`RequireAtLeastOne`**: Define un tipo que requiere al menos una propiedad de un conjunto de propiedades.\n  ```typescript\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n  ```\n\n- **`CustomLLMWithOptionals`**: Define la estructura de un modelo de IA personalizado con opciones opcionales.\n  ```typescript\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      signal: AbortSignal,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      signal: AbortSignal,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n  ```\n\n- **`CustomLLM`**: Define la estructura de un modelo de IA personalizado.\n  ```typescript\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n  ```\n\n### Interfaces y Tipos para IDE\n- **`DiffLineType`**: Define los tipos posibles de línea de diferencia.\n  ```typescript\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n  ```\n\n- **`DiffLine`**: Define la estructura de una línea de diferencia.\n  ```typescript\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n  ```\n\n- **`Problem`**: Define la estructura de un problema.\n  ```typescript\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n  ```\n\n- **`Thread`**: Define la estructura de un hilo.\n  ```typescript\n  export class Thread {\n    name: string;\n    id: number;\n  }\n  ```\n\n- **`IdeType`**: Define los tipos posibles de IDE.\n  ```typescript\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  ```\n\n- **`IdeInfo`**: Define la estructura de la información del IDE.\n  ```typescript\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n  ```\n\n- **`BranchAndDir`**: Define la estructura de una rama y directorio.\n  ```typescript\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n  ```\n\n- **`IndexTag`**: Define la estructura de una etiqueta de índice.\n  ```typescript\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n  ```\n\n- **`FileType`**: Define los tipos posibles de archivo.\n  ```typescript\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n  ```\n\n- **`IdeSettings`**: Define la estructura de la configuración del IDE.\n  ```typescript\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    pauseCodebaseIndexOnStart: boolean;\n  }\n  ```\n\n- **`IDE`**: Define la estructura de un IDE.\n  ```typescript\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(includeUnstaged: boolean): Promise<string[]>;\n    getClipboardContent(): Promise<{ text: string; copiedAt: string }>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileExists(filepath: string): Promise<boolean>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    openFile(path: string): Promise<void>;\n    openUrl(url: string): Promise<void>;\n    runCommand(command: string): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getCurrentFile(): Promise<\n      | undefined\n      | {\n          isUntitled: boolean;\n          path: string;\n          contents: string;\n        }\n    >;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    subprocess(command: string, cwd?: string): Promise<[string, string]>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    showToast(\n      type: ToastType,\n      message: string,\n      ...otherParams: any[]\n    ): Promise<any>;\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n  }\n  ```\n\n### Interfaces y Tipos para SDK de Continue\n- **`ContinueSDK`**: Define la estructura del SDK de Continue.\n  ```typescript\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n  ```\n\n### Interfaces y Tipos para Comandos de Barra Diagonal\n- **`SlashCommand`**: Define la estructura de un comando de barra diagonal.\n  ```typescript\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n  ```\n\n### Interfaces y Tipos para Configuración\n- **`StepName`**: Define los nombres posibles de pasos.\n  ```typescript\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n  ```\n\n- **`ContextProviderName`**: Define los nombres posibles de proveedores de contexto.\n  ```typescript\n  type ContextProviderName =\n    | \"diff\"\n    | \"terminal\"\n    | \"debugger\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\"\n    | \"greptile\"\n    | \"outline\"\n    | \"continue-proxy\"\n    | \"highlights\"\n    | \"file\"\n    | \"issue\"\n    | \"repo-map\"\n    | \"url\"\n    | string;\n  ```\n\n- **`TemplateType`**: Define los tipos posibles de plantillas.\n  ```typescript\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"granite\"\n    | \"llama3\"\n    | \"codestral\";\n  ```\n\n- **`RequestOptions`**: Define la estructura de las opciones de solicitud.\n  ```typescript\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n  ```\n\n- **`CacheBehavior`**: Define la estructura del comportamiento de caché.\n  ```typescript\n  export interface CacheBehavior {\n    cacheSystemMessage?: boolean;\n    cacheConversation?: boolean;\n  }\n  ```\n\n- **`ClientCertificateOptions`**: Define la estructura de las opciones de certificado de cliente.\n  ```typescript\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n  ```\n\n- **`StepWithParams`**: Define la estructura de un paso con parámetros.\n  ```typescript\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n  ```\n\n- **`ContextProviderWithParams`**: Define la estructura de un proveedor de contexto con parámetros.\n  ```typescript\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n  ```\n\n- **`SlashCommandDescription`**: Define la estructura de una descripción de comando de barra diagonal.\n  ```typescript\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n  ```\n\n- **`CustomCommand`**: Define la estructura de un comando personalizado.\n  ```typescript\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n  ```\n\n- **`Prediction`**: Define la estructura de una predicción.\n  ```typescript\n  interface Prediction {\n    type: \"content\";\n    content:\n      | string\n      | {\n          type: \"text\";\n          text: string;\n        }[];\n  }\n  ```\n\n- **`ToolExtras`**: Define la estructura de herramientas adicionales.\n  ```typescript\n  export interface ToolExtras {\n    ide: IDE;\n    llm: ILLM;\n    fetch: FetchFunction;\n  }\n  ```\n\n- **`Tool`**: Define la estructura de una herramienta.\n  ```typescript\n  export interface Tool {\n    type: \"function\";\n    function: {\n      name: string;\n      description?: string;\n      parameters?: Record<string, any>;\n      strict?: boolean | null;\n    };\n    displayTitle: string;\n    wouldLikeTo: string;\n    readonly: boolean;\n    uri?: string;\n  }\n  ```\n\n- **`BaseCompletionOptions`**: Define la estructura base de las opciones de completación.\n  ```typescript\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    useMmap?: boolean;\n    keepAlive?: number;\n    numGpu?: number;\n    raw?: boolean;\n    stream?: boolean;\n    prediction?: Prediction;\n    tools?: Tool[];\n  }\n  ```\n\n- **`ModelCapability`**: Define la estructura de las capacidades de un modelo.\n  ```typescript\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n  ```\n\n- **`ModelDescription`**: Define la estructura de una descripción de modelo.\n  ```typescript\n  export interface ModelDescription {\n    title: string;\n    provider: string;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n    cacheBehavior?: CacheBehavior;\n  }\n  ```\n\n- **`JSONEmbedOptions`**: Define la estructura de las opciones de incrustación JSON.\n  ```typescript\n  export interface JSONEmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    deployment?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n    maxBatchSize?: number;\n    profile?: string;\n    region?: string;\n    projectId?: string;\n  }\n  ```\n\n- **`EmbeddingsProviderDescription`**: Define la estructura de una descripción de proveedor de incrustaciones.\n  ```typescript\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: string;\n  }\n  ```\n\n- **`RerankerDescription`**: Define la estructura de una descripción de clasificador.\n  ```typescript\n  export interface RerankerDescription {\n    name: string;\n    params?: { [key: string]: any };\n  }\n  ```\n\n- **`TabAutocompleteOptions`**: Define la estructura de las opciones de autocompletado de pestañas.\n  ```typescript\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    transform?: boolean;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    useCache: boolean;\n    onlyMy","modelTitle":"Codestral (1)","modelProvider":"mistral","sessionId":"7b2a84d2-8ae3-49db-a3c4-9c31519168b2"}
