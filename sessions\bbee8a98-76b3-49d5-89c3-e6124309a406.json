{"codeblocks": [], "history": [{"contextItems": [{"from": "editor", "meta": {"type": "mention", "mentionItemId": "file", "query": "c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts", "title": "index.d.ts"}, "hash": "34c5907744a7cc84", "status": "done", "id": "4c00bc25-845b-4455-ba24-810e2937b84c", "preview": {"detail": {"type": "file", "filepath": "c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts", "content": "\ndeclare global {\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status: \"loading\" | \"indexing\" | \"done\" | \"failed\" | \"paused\" | \"disabled\";\n    debugInfo?: string;\n  }\n\n  export type PromptTemplate =\n    | string\n    | ((\n        history: ChatMessage[],\n        otherData: Record<string, string>,\n      ) => string | ChatMessage[]);\n\n  export interface ILLM extends LLMOptions {\n    get providerName(): ModelProvider;\n\n    uniqueId: string;\n    model: string;\n\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n\n    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;\n\n    streamComplete(\n      prompt: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamFim(\n      prefix: string,\n      suffix: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamChat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n\n    chat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n\n    countTokens(text: string): number;\n\n    supportsImages(): boolean;\n\n    supportsCompletions(): boolean;\n\n    supportsPrefill(): boolean;\n\n    supportsFim(): boolean;\n\n    listModels(): Promise<string[]>;\n\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n\n  export interface ContextProviderDescription {\n    title: string;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n  }\n\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: EmbeddingsProvider;\n    reranker: Reranker | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    rootUrl?: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n\n  export interface SiteIndexingConfig {\n    startUrl: string;\n    rootUrl?: string;\n    title: string;\n    maxDepth?: number;\n  }\n\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface PersistedSessionInfo {\n    history: ChatHistory;\n    title: string;\n    workspaceDirectory: string;\n    sessionId: string;\n  }\n\n  export interface SessionInfo {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n\n  export interface RangeInFile {\n    filepath: string;\n    content?: string\n    range: Range;\n  }\n\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\";\n\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n\n  export type MessageContent = string | MessagePart[];\n\n  export interface ChatMessage {\n    role: ChatMessageRole;\n    content: MessageContent;\n  }\n\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface ContextItemWithId {\n    content: string;\n    name: string;\n    description: string;\n    id: ContextItemId;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    editorState?: any;\n    modifiers?: InputModifiers;\n    contextItems: ContextItemWithId[];\n    promptLogs?: PromptLog[];\n  }\n\n  export type ChatHistory = ChatHistoryItem[];\n\n  // LLM\n\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n\n    model?: string;\n  }\n  export interface LLMOptions {\n    model: string;\n\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n\n    useLegacyCompletionsEndpoint?: boolean;\n\n    // Cloudflare options\n    accountId?: string;\n\n    // Azure options\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n\n    // AWS options\n    profile?: string;\n    modelArn?: string;\n\n    // AWS and GCP Options\n    region?: string;\n\n    // GCP Options\n    projectId?: string;\n    capabilities?: ModelCapability;\n\n    // IBM watsonx options\n    watsonxUrl?: string;\n    watsonxCreds?: string;\n    watsonxProjectId?: string;\n    watsonxStopToken?: string;\n    watsonxApiVersion?: string;\n\n    cacheSystemMessage?: boolean;\n  }\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n\n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n\n  // IDE\n\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n\n  export class Thread {\n    name: string;\n    id: number;\n  }\n\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    enableControlServerBeta: boolean;\n    pauseCodebaseIndexOnStart: boolean;\n    enableDebugLogs: boolean;\n  }\n\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(): Promise<string>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    listFolders(): Promise<string[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileStat(filepath: string): Promise<{exists: boolean, type: number}>;\n    readRawFile(filepath: string): Promise<string>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    getContinueDir(): Promise<string>;\n    openFile(path: string): Promise<void>;\n    runCommand(command: string, shouldExecute?: boolean): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    showDiff(\n      filepath: string,\n      newContents: string,\n      stepIndex: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getOpenEditors(): Promise<string[]>;\n    getCurrentFile(): Promise<string | undefined>;\n    getCurrentFileSelection(): Promise<RangeInFile | undefined>;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    errorPopup(message: string): Promise<void>;\n    infoPopup(message: string): Promise<void>;\n\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    getGitHubAuthToken(): Promise<string | undefined>;\n\n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n\n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n    onDidChangeActiveTextSelection(callback: (selection?: Range) => void): void\n    pathSep(): Promise<string>;\n  }\n\n  // Slash Commands\n\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n\n  // Config\n\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n\n  type ContextProviderName =\n    | \"diff\"\n    | \"github\"\n    | \"terminal\"\n    | \"locals\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\";\n\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"llama3\";\n\n  type ModelProvider =\n    | \"openai\"\n    | \"free-trial\"\n    | \"anthropic\"\n    | \"cohere\"\n    | \"together\"\n    | \"ollama\"\n    | \"huggingface-tgi\"\n    | \"huggingface-inference-api\"\n    | \"kindo\"\n    | \"llama.cpp\"\n    | \"replicate\"\n    | \"text-gen-webui\"\n    | \"lmstudio\"\n    | \"llamafile\"\n    | \"gemini\"\n    | \"mistral\"\n    | \"bedrock\"\n    | \"bedrockimport\"\n    | \"sagemaker\"\n    | \"deepinfra\"\n    | \"flowise\"\n    | \"groq\"\n    | \"continue-proxy\"\n    | \"fireworks\"\n    | \"custom\"\n    | \"cloudflare\"\n    | \"deepseek\"\n    | \"azure\"\n    | \"openai-aiohttp\"\n    | \"msty\"\n    | \"watsonx\"\n    | \"openrouter\"\n    | \"nvidia\";\n\n  export type ModelName =\n    | \"AUTODETECT\"\n    // OpenAI\n    | \"gpt-3.5-turbo\"\n    | \"gpt-3.5-turbo-16k\"\n    | \"gpt-4\"\n    | \"gpt-3.5-turbo-0613\"\n    | \"gpt-4-32k\"\n    | \"gpt-4o\"\n    | \"gpt-4o-mini\"\n    | \"gpt-4-turbo\"\n    | \"gpt-4-turbo-preview\"\n    | \"gpt-4-vision-preview\"\n    // Mistral\n    | \"codestral-latest\"\n    | \"open-mistral-7b\"\n    | \"open-mixtral-8x7b\"\n    | \"open-mixtral-8x22b\"\n    | \"mistral-small-latest\"\n    | \"mistral-large-latest\"\n    | \"mistral-7b\"\n    | \"mistral-8x7b\"\n    // Llama 2\n    | \"llama2-7b\"\n    | \"llama2-13b\"\n    | \"llama2-70b\"\n    | \"codellama-7b\"\n    | \"codellama-13b\"\n    | \"codellama-34b\"\n    | \"codellama-70b\"\n    // Llama 3\n    | \"llama3-8b\"\n    | \"llama3-70b\"\n    // Other Open-source\n    | \"phi2\"\n    | \"phind-codellama-34b\"\n    | \"wizardcoder-7b\"\n    | \"wizardcoder-13b\"\n    | \"wizardcoder-34b\"\n    | \"zephyr-7b\"\n    | \"codeup-13b\"\n    | \"deepseek-7b\"\n    | \"deepseek-33b\"\n    | \"neural-chat-7b\"\n    // Anthropic\n    | \"claude-3-5-sonnet-20240620\"\n    | \"claude-3-opus-20240229\"\n    | \"claude-3-sonnet-20240229\"\n    | \"claude-3-haiku-20240307\"\n    | \"claude-2.1\"\n    | \"claude-2\"\n    // Cohere\n    | \"command-r\"\n    | \"command-r-plus\"\n    // Gemini\n    | \"gemini-pro\"\n    | \"gemini-1.5-pro-latest\"\n    | \"gemini-1.5-pro\"\n    | \"gemini-1.5-flash-latest\"\n    | \"gemini-1.5-flash\"\n    // Mistral\n    | \"mistral-tiny\"\n    | \"mistral-small\"\n    | \"mistral-medium\"\n    // Tab autocomplete\n    | \"deepseek-1b\"\n    | \"starcoder-1b\"\n    | \"starcoder-3b\"\n    | \"starcoder2-3b\"\n    | \"stable-code-3b\";\n\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    keepAlive?: number;\n    raw?: boolean;\n    stream?: boolean;\n  }\n\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n\n  export interface ModelDescription {\n    title: string;\n    provider: ModelProvider;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n  }\n\n  export type EmbeddingsProviderName =\n    | \"huggingface-tei\"\n    | \"transformers.js\"\n    | \"ollama\"\n    | \"openai\"\n    | \"cohere\"\n    | \"free-trial\"\n    | \"gemini\"\n    | \"continue-proxy\"\n    | \"deepinfra\"\n    | \"voyage\";\n\n  export interface EmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    engine?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n  }\n\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: EmbeddingsProviderName;\n  }\n\n  export interface EmbeddingsProvider {\n    id: string;\n    providerName: EmbeddingsProviderName;\n    maxChunkSize: number;\n    embed(chunks: string[]): Promise<number[][]>;\n  }\n\n  export type RerankerName =\n    | \"cohere\"\n    | \"voyage\"\n    | \"llm\"\n    | \"free-trial\"\n    | \"huggingface-tei\"\n    | \"continue-proxy\";\n\n  export interface RerankerDescription {\n    name: RerankerName;\n    params?: { [key: string]: any };\n  }\n\n  export interface Reranker {\n    name: string;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  }\n\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    useCopyBuffer: boolean;\n    useFileSuffix: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    maxSnippetPercentage: number;\n    recentlyEditedSimilarityThreshold: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useOtherFiles: boolean;\n    useRecentlyEdited: boolean;\n    recentLinePrefixMatchMinLength: number;\n    disableInFiles?: string[];\n    useImports?: boolean;\n  }\n\n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n  }\n\n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n\n  interface ModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n  }\n\n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n\n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n\n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n\n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n\n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n\n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n\n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  }\n\n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n\n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n\n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n\n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | EmbeddingsProvider;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | Reranker;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n\n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: EmbeddingsProvider;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: Reranker;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n  }\n\n}\n\nexport {};\n"}}}, {"from": "modifier", "meta": {"type": "currentFile"}, "hash": "e051e4087aa9657a", "id": "8381861f-2d60-4f71-a6c8-1058ebaf5bb6", "status": "updating", "preview": {"detail": {"type": "file", "filepath": ".monica-code/types/core/index.d.ts", "content": ""}}}], "editorState": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "slashcommand", "attrs": {"id": "/read-large-file", "label": "/read-large-file"}}, {"type": "text", "text": "  "}, {"type": "mention", "attrs": {"id": "c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts", "label": "index.d.ts", "renderInlineAs": null, "query": "c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts", "itemType": "file"}}, {"type": "text", "text": " "}]}]}, "id": "a7bd7634-3277-489b-9857-665613930f0e", "content": [{"type": "text", "text": "/read-large-file index.d.ts "}], "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065409764, "parentId": "__root__", "role": "user"}, {"codeblockIds": [], "contextResults": [{"name": "index.d.ts", "description": "c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts", "content": "```c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts\n\ndeclare global {\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status: \"loading\" | \"indexing\" | \"done\" | \"failed\" | \"paused\" | \"disabled\";\n    debugInfo?: string;\n  }\n\n  export type PromptTemplate =\n    | string\n    | ((\n        history: ChatMessage[],\n        otherData: Record<string, string>,\n      ) => string | ChatMessage[]);\n\n  export interface ILLM extends LLMOptions {\n    get providerName(): ModelProvider;\n\n    uniqueId: string;\n    model: string;\n\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n\n    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;\n\n    streamComplete(\n      prompt: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamFim(\n      prefix: string,\n      suffix: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamChat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n\n    chat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n\n    countTokens(text: string): number;\n\n    supportsImages(): boolean;\n\n    supportsCompletions(): boolean;\n\n    supportsPrefill(): boolean;\n\n    supportsFim(): boolean;\n\n    listModels(): Promise<string[]>;\n\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n\n  export interface ContextProviderDescription {\n    title: string;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n  }\n\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: EmbeddingsProvider;\n    reranker: Reranker | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    rootUrl?: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n\n  export interface SiteIndexingConfig {\n    startUrl: string;\n    rootUrl?: string;\n    title: string;\n    maxDepth?: number;\n  }\n\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface PersistedSessionInfo {\n    history: ChatHistory;\n    title: string;\n    workspaceDirectory: string;\n    sessionId: string;\n  }\n\n  export interface SessionInfo {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n\n  export interface RangeInFile {\n    filepath: string;\n    content?: string\n    range: Range;\n  }\n\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\";\n\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n\n  export type MessageContent = string | MessagePart[];\n\n  export interface ChatMessage {\n    role: ChatMessageRole;\n    content: MessageContent;\n  }\n\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface ContextItemWithId {\n    content: string;\n    name: string;\n    description: string;\n    id: ContextItemId;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    editorState?: any;\n    modifiers?: InputModifiers;\n    contextItems: ContextItemWithId[];\n    promptLogs?: PromptLog[];\n  }\n\n  export type ChatHistory = ChatHistoryItem[];\n\n  // LLM\n\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n\n    model?: string;\n  }\n  export interface LLMOptions {\n    model: string;\n\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n\n    useLegacyCompletionsEndpoint?: boolean;\n\n    // Cloudflare options\n    accountId?: string;\n\n    // Azure options\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n\n    // AWS options\n    profile?: string;\n    modelArn?: string;\n\n    // AWS and GCP Options\n    region?: string;\n\n    // GCP Options\n    projectId?: string;\n    capabilities?: ModelCapability;\n\n    // IBM watsonx options\n    watsonxUrl?: string;\n    watsonxCreds?: string;\n    watsonxProjectId?: string;\n    watsonxStopToken?: string;\n    watsonxApiVersion?: string;\n\n    cacheSystemMessage?: boolean;\n  }\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n\n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n\n  // IDE\n\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n\n  export class Thread {\n    name: string;\n    id: number;\n  }\n\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    enableControlServerBeta: boolean;\n    pauseCodebaseIndexOnStart: boolean;\n    enableDebugLogs: boolean;\n  }\n\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(): Promise<string>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    listFolders(): Promise<string[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileStat(filepath: string): Promise<{exists: boolean, type: number}>;\n    readRawFile(filepath: string): Promise<string>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    getContinueDir(): Promise<string>;\n    openFile(path: string): Promise<void>;\n    runCommand(command: string, shouldExecute?: boolean): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    showDiff(\n      filepath: string,\n      newContents: string,\n      stepIndex: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getOpenEditors(): Promise<string[]>;\n    getCurrentFile(): Promise<string | undefined>;\n    getCurrentFileSelection(): Promise<RangeInFile | undefined>;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    errorPopup(message: string): Promise<void>;\n    infoPopup(message: string): Promise<void>;\n\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    getGitHubAuthToken(): Promise<string | undefined>;\n\n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n\n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n    onDidChangeActiveTextSelection(callback: (selection?: Range) => void): void\n    pathSep(): Promise<string>;\n  }\n\n  // Slash Commands\n\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n\n  // Config\n\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n\n  type ContextProviderName =\n    | \"diff\"\n    | \"github\"\n    | \"terminal\"\n    | \"locals\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\";\n\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"llama3\";\n\n  type ModelProvider =\n    | \"openai\"\n    | \"free-trial\"\n    | \"anthropic\"\n    | \"cohere\"\n    | \"together\"\n    | \"ollama\"\n    | \"huggingface-tgi\"\n    | \"huggingface-inference-api\"\n    | \"kindo\"\n    | \"llama.cpp\"\n    | \"replicate\"\n    | \"text-gen-webui\"\n    | \"lmstudio\"\n    | \"llamafile\"\n    | \"gemini\"\n    | \"mistral\"\n    | \"bedrock\"\n    | \"bedrockimport\"\n    | \"sagemaker\"\n    | \"deepinfra\"\n    | \"flowise\"\n    | \"groq\"\n    | \"continue-proxy\"\n    | \"fireworks\"\n    | \"custom\"\n    | \"cloudflare\"\n    | \"deepseek\"\n    | \"azure\"\n    | \"openai-aiohttp\"\n    | \"msty\"\n    | \"watsonx\"\n    | \"openrouter\"\n    | \"nvidia\";\n\n  export type ModelName =\n    | \"AUTODETECT\"\n    // OpenAI\n    | \"gpt-3.5-turbo\"\n    | \"gpt-3.5-turbo-16k\"\n    | \"gpt-4\"\n    | \"gpt-3.5-turbo-0613\"\n    | \"gpt-4-32k\"\n    | \"gpt-4o\"\n    | \"gpt-4o-mini\"\n    | \"gpt-4-turbo\"\n    | \"gpt-4-turbo-preview\"\n    | \"gpt-4-vision-preview\"\n    // Mistral\n    | \"codestral-latest\"\n    | \"open-mistral-7b\"\n    | \"open-mixtral-8x7b\"\n    | \"open-mixtral-8x22b\"\n    | \"mistral-small-latest\"\n    | \"mistral-large-latest\"\n    | \"mistral-7b\"\n    | \"mistral-8x7b\"\n    // Llama 2\n    | \"llama2-7b\"\n    | \"llama2-13b\"\n    | \"llama2-70b\"\n    | \"codellama-7b\"\n    | \"codellama-13b\"\n    | \"codellama-34b\"\n    | \"codellama-70b\"\n    // Llama 3\n    | \"llama3-8b\"\n    | \"llama3-70b\"\n    // Other Open-source\n    | \"phi2\"\n    | \"phind-codellama-34b\"\n    | \"wizardcoder-7b\"\n    | \"wizardcoder-13b\"\n    | \"wizardcoder-34b\"\n    | \"zephyr-7b\"\n    | \"codeup-13b\"\n    | \"deepseek-7b\"\n    | \"deepseek-33b\"\n    | \"neural-chat-7b\"\n    // Anthropic\n    | \"claude-3-5-sonnet-20240620\"\n    | \"claude-3-opus-20240229\"\n    | \"claude-3-sonnet-20240229\"\n    | \"claude-3-haiku-20240307\"\n    | \"claude-2.1\"\n    | \"claude-2\"\n    // Cohere\n    | \"command-r\"\n    | \"command-r-plus\"\n    // Gemini\n    | \"gemini-pro\"\n    | \"gemini-1.5-pro-latest\"\n    | \"gemini-1.5-pro\"\n    | \"gemini-1.5-flash-latest\"\n    | \"gemini-1.5-flash\"\n    // Mistral\n    | \"mistral-tiny\"\n    | \"mistral-small\"\n    | \"mistral-medium\"\n    // Tab autocomplete\n    | \"deepseek-1b\"\n    | \"starcoder-1b\"\n    | \"starcoder-3b\"\n    | \"starcoder2-3b\"\n    | \"stable-code-3b\";\n\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    keepAlive?: number;\n    raw?: boolean;\n    stream?: boolean;\n  }\n\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n\n  export interface ModelDescription {\n    title: string;\n    provider: ModelProvider;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n  }\n\n  export type EmbeddingsProviderName =\n    | \"huggingface-tei\"\n    | \"transformers.js\"\n    | \"ollama\"\n    | \"openai\"\n    | \"cohere\"\n    | \"free-trial\"\n    | \"gemini\"\n    | \"continue-proxy\"\n    | \"deepinfra\"\n    | \"voyage\";\n\n  export interface EmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    engine?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n  }\n\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: EmbeddingsProviderName;\n  }\n\n  export interface EmbeddingsProvider {\n    id: string;\n    providerName: EmbeddingsProviderName;\n    maxChunkSize: number;\n    embed(chunks: string[]): Promise<number[][]>;\n  }\n\n  export type RerankerName =\n    | \"cohere\"\n    | \"voyage\"\n    | \"llm\"\n    | \"free-trial\"\n    | \"huggingface-tei\"\n    | \"continue-proxy\";\n\n  export interface RerankerDescription {\n    name: RerankerName;\n    params?: { [key: string]: any };\n  }\n\n  export interface Reranker {\n    name: string;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  }\n\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    useCopyBuffer: boolean;\n    useFileSuffix: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    maxSnippetPercentage: number;\n    recentlyEditedSimilarityThreshold: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useOtherFiles: boolean;\n    useRecentlyEdited: boolean;\n    recentLinePrefixMatchMinLength: number;\n    disableInFiles?: string[];\n    useImports?: boolean;\n  }\n\n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n  }\n\n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n\n  interface ModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n  }\n\n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n\n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n\n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n\n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n\n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n\n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n\n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  }\n\n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n\n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n\n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n\n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | EmbeddingsProvider;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | Reranker;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n\n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: EmbeddingsProvider;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: Reranker;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n  }\n\n}\n\nexport {};\n\n```", "uri": {"type": "file", "value": "c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts"}, "id": {"providerTitle": "file", "itemId": "c6416ecd-d6f6-45fe-be30-4859c63f0dfa"}}, {"description": ".monica-code/types/core/index.d.ts", "content": "This is the currently open file:\n\n```.monica-code/types/core/index.d.ts\n\n```", "name": "index.d.ts", "uri": {"type": "file", "value": ".monica-code/types/core/index.d.ts"}, "id": {"providerTitle": "currentFile", "itemId": "2a9c2138-fa6f-4907-a733-cd67d3315810"}}], "isComplete": true, "promptLogs": [], "modelName": "Claude 3.7 Sonnet Thinking", "reasoningContent": "", "id": "279808ae-9c7d-4fce-aa28-0bcfe378b297", "content": "# TypeScript Declaration File Analysis: Core Type System\n\nI've analyzed the provided TypeScript declaration file (`index.d.ts`), which defines a comprehensive type system for what appears to be an AI coding assistant named \"<PERSON>tinue\" (based on numerous references throughout the file).\n\n## File Overview\n\nThis is a global declaration file that defines the core type system for the application, making these types available throughout the codebase without explicit imports. The file establishes a robust foundation for an AI-powered coding assistant with extensive IDE integration.\n\n## Key Components\n\n### 1. LLM (Large Language Model) System\n- `ILLM` interface defines core LLM functionality including:\n  - Methods for text completion, chat, token counting\n  - Streaming capabilities\n  - Support for numerous model providers (OpenAI, Anthropic, Cohere, etc.)\n- Comprehensive configuration options via `LLMOptions`, `CompletionOptions`, etc.\n- Support for customizing model behavior through templates and system messages\n\n### 2. Context Provider System\n- `IContextProvider` and related interfaces create a pluggable system for providing context\n- Context items can be retrieved from various sources (codebase, terminal, problems, etc.)\n- Custom context providers can be implemented\n\n### 3. IDE Integration\n- Extensive `IDE` interface for interacting with editors like VSCode and JetBrains\n- File operations (read, write, diff, stat)\n- Editor state (selections, open files, problems)\n- Command execution and UI interactions\n\n### 4. Chat System\n- `ChatMessage`, `ChatHistory` interfaces for conversation management\n- Support for multi-modal content (text + images)\n- Different message roles (user, assistant, system)\n\n### 5. Configuration System\n- Multiple configuration interfaces (`ContinueConfig`, `SerializedContinueConfig`)\n- Configuration for models, providers, UI options, etc.\n- Merge behavior for combining different configuration sources\n\n### 6. Advanced Features\n- Slash commands system for custom commands\n- Embeddings and reranking for semantic search\n- Tab autocompletion system\n- Code chunking for analysis\n- Experimental features (context menu prompts, quick actions)\n\n## Architecture Insights\n\nThe type system reveals a well-structured application with clear separation of concerns:\n- Model interaction is abstracted through interfaces\n- IDE integration is comprehensive but modular\n- Plugin-like systems for context providers and slash commands\n- Configuration is flexible and extensible\n\nThis appears to be a sophisticated AI coding assistant tool that can integrate with development environments and provide contextual assistance using various LLM providers.", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065409764, "parentId": "a7bd7634-3277-489b-9857-665613930f0e", "role": "assistant"}, {"contextItems": [{"from": "modifier", "meta": {"type": "mention", "mentionItemId": "file", "title": "AUTONOMOUS-AGENT-GUIDE.md", "query": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, "hash": "c2b107cc62b48961", "status": "done", "id": "0add7f72-98a3-4a8c-bfb3-6ee0f38eee5b", "preview": {"detail": {"type": "file", "filepath": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md", "content": ""}}}, {"from": "modifier", "meta": {"type": "mention", "mentionItemId": "file", "title": "AGENT-ANALYSIS.md", "query": ".monica-code/AGENT-ANALYSIS.md"}, "hash": "7d70203a2a0ea8cf", "status": "done", "id": "dc1dc21a-80b6-4c11-86bf-6223682c75c2", "preview": {"detail": {"type": "file", "filepath": ".monica-code/AGENT-ANALYSIS.md", "content": ""}}}, {"from": "modifier", "meta": {"type": "mention", "mentionItemId": "file", "title": "README.md", "query": ".monica-code/README.md"}, "hash": "03cb21c53e77ce99", "status": "done", "id": "4aefb0a9-2fd2-43e8-9815-109f868f286a", "preview": {"detail": {"type": "file", "filepath": ".monica-code/README.md", "content": ""}}}, {"from": "modifier", "meta": {"type": "mention", "mentionItemId": "file", "title": "LARGE-FILE-HANDLING-GUIDE.md", "query": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, "hash": "7c2d5841afa73055", "status": "done", "id": "f66fb77a-1e7c-4668-a870-cc530f05bd1b", "preview": {"detail": {"type": "file", "filepath": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md", "content": ""}}}, {"from": "modifier", "meta": {"type": "currentFile"}, "hash": "e051e4087aa9657a", "id": "8381861f-2d60-4f71-a6c8-1058ebaf5bb6", "status": "updating", "preview": {"detail": {"type": "file", "filepath": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md", "content": ""}}}], "editorState": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "lee estoa 4 archivos a la vez"}]}]}, "id": "68a7d992-82fc-4350-8d33-f07a2c66894d", "content": [{"type": "text", "text": "lee estoa 4 archivos a la vez"}], "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065505513, "parentId": "279808ae-9c7d-4fce-aa28-0bcfe378b297", "role": "user"}, {"codeblockIds": [], "contextResults": [{"name": "AGENT-ANALYSIS.md", "description": ".monica-code/AGENT-ANALYSIS.md", "content": "```.monica-code/AGENT-ANALYSIS.md\n\n```", "uri": {"type": "file", "value": ".monica-code/AGENT-ANALYSIS.md"}, "id": {"providerTitle": "file", "itemId": "8d3d5b9e-d631-4f32-9a2e-bcda54d80792"}}, {"name": "README.md", "description": ".monica-code/README.md", "content": "```.monica-code/README.md\n\n```", "uri": {"type": "file", "value": ".monica-code/README.md"}, "id": {"providerTitle": "file", "itemId": "48e4ae4e-4eeb-42ee-aaaa-4a16adcb5567"}}, {"name": "LARGE-FILE-HANDLING-GUIDE.md", "description": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md", "content": "```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```", "uri": {"type": "file", "value": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, "id": {"providerTitle": "file", "itemId": "2ec21c8c-da32-4943-acd9-a25ff1a2be46"}}, {"description": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md", "content": "This is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```", "name": "AUTONOMOUS-AGENT-GUIDE.md", "uri": {"type": "file", "value": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, "id": {"providerTitle": "currentFile", "itemId": "4a6567bf-dfa5-4786-a583-983e41186b00"}}], "isComplete": true, "promptLogs": [{"modelTitle": "Claude 3.7 Sonnet Thinking", "prompt": "<system>\nWhen you need to edit or create new files, use the format ```lang:path at the start of a code block. For example:\n\n  - To edit an existing file: ```ts:path/to/edit.ts\n  - To create a new file: ```py:path/to/new/file.py\n  \n  The path can be either:\n  - Absolute (e.g., /Users/<USER>/project/file.js)\n  - Relative (e.g., src/components/Button.tsx, starting WITHOUT '/')\n  \n  The path must be complete and clear. When providing edits instead of the complete file, include clear comments explaining the changes.\n\n  Always respond in the same language that the user writes in. If the user messages in English, respond in English; if in Chinese, respond in Chinese; and so forth. Language matching is crucial for effective communication.\n\nEres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones.\nCurrent Date is: 2025-06-04T19:31:45.569Z\n\n<user>\n```c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts\n\ndeclare global {\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status: \"loading\" | \"indexing\" | \"done\" | \"failed\" | \"paused\" | \"disabled\";\n    debugInfo?: string;\n  }\n\n  export type PromptTemplate =\n    | string\n    | ((\n        history: ChatMessage[],\n        otherData: Record<string, string>,\n      ) => string | ChatMessage[]);\n\n  export interface ILLM extends LLMOptions {\n    get providerName(): ModelProvider;\n\n    uniqueId: string;\n    model: string;\n\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n\n    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;\n\n    streamComplete(\n      prompt: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamFim(\n      prefix: string,\n      suffix: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamChat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n\n    chat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n\n    countTokens(text: string): number;\n\n    supportsImages(): boolean;\n\n    supportsCompletions(): boolean;\n\n    supportsPrefill(): boolean;\n\n    supportsFim(): boolean;\n\n    listModels(): Promise<string[]>;\n\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n\n  export interface ContextProviderDescription {\n    title: string;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n  }\n\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: EmbeddingsProvider;\n    reranker: Reranker | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    rootUrl?: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n\n  export interface SiteIndexingConfig {\n    startUrl: string;\n    rootUrl?: string;\n    title: string;\n    maxDepth?: number;\n  }\n\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface PersistedSessionInfo {\n    history: ChatHistory;\n    title: string;\n    workspaceDirectory: string;\n    sessionId: string;\n  }\n\n  export interface SessionInfo {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n\n  export interface RangeInFile {\n    filepath: string;\n    content?: string\n    range: Range;\n  }\n\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\";\n\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n\n  export type MessageContent = string | MessagePart[];\n\n  export interface ChatMessage {\n    role: ChatMessageRole;\n    content: MessageContent;\n  }\n\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface ContextItemWithId {\n    content: string;\n    name: string;\n    description: string;\n    id: ContextItemId;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    editorState?: any;\n    modifiers?: InputModifiers;\n    contextItems: ContextItemWithId[];\n    promptLogs?: PromptLog[];\n  }\n\n  export type ChatHistory = ChatHistoryItem[];\n\n  // LLM\n\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n\n    model?: string;\n  }\n  export interface LLMOptions {\n    model: string;\n\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n\n    useLegacyCompletionsEndpoint?: boolean;\n\n    // Cloudflare options\n    accountId?: string;\n\n    // Azure options\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n\n    // AWS options\n    profile?: string;\n    modelArn?: string;\n\n    // AWS and GCP Options\n    region?: string;\n\n    // GCP Options\n    projectId?: string;\n    capabilities?: ModelCapability;\n\n    // IBM watsonx options\n    watsonxUrl?: string;\n    watsonxCreds?: string;\n    watsonxProjectId?: string;\n    watsonxStopToken?: string;\n    watsonxApiVersion?: string;\n\n    cacheSystemMessage?: boolean;\n  }\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n\n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n\n  // IDE\n\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n\n  export class Thread {\n    name: string;\n    id: number;\n  }\n\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    enableControlServerBeta: boolean;\n    pauseCodebaseIndexOnStart: boolean;\n    enableDebugLogs: boolean;\n  }\n\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(): Promise<string>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    listFolders(): Promise<string[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileStat(filepath: string): Promise<{exists: boolean, type: number}>;\n    readRawFile(filepath: string): Promise<string>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    getContinueDir(): Promise<string>;\n    openFile(path: string): Promise<void>;\n    runCommand(command: string, shouldExecute?: boolean): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    showDiff(\n      filepath: string,\n      newContents: string,\n      stepIndex: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getOpenEditors(): Promise<string[]>;\n    getCurrentFile(): Promise<string | undefined>;\n    getCurrentFileSelection(): Promise<RangeInFile | undefined>;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    errorPopup(message: string): Promise<void>;\n    infoPopup(message: string): Promise<void>;\n\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    getGitHubAuthToken(): Promise<string | undefined>;\n\n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n\n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n    onDidChangeActiveTextSelection(callback: (selection?: Range) => void): void\n    pathSep(): Promise<string>;\n  }\n\n  // Slash Commands\n\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n\n  // Config\n\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n\n  type ContextProviderName =\n    | \"diff\"\n    | \"github\"\n    | \"terminal\"\n    | \"locals\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\";\n\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"llama3\";\n\n  type ModelProvider =\n    | \"openai\"\n    | \"free-trial\"\n    | \"anthropic\"\n    | \"cohere\"\n    | \"together\"\n    | \"ollama\"\n    | \"huggingface-tgi\"\n    | \"huggingface-inference-api\"\n    | \"kindo\"\n    | \"llama.cpp\"\n    | \"replicate\"\n    | \"text-gen-webui\"\n    | \"lmstudio\"\n    | \"llamafile\"\n    | \"gemini\"\n    | \"mistral\"\n    | \"bedrock\"\n    | \"bedrockimport\"\n    | \"sagemaker\"\n    | \"deepinfra\"\n    | \"flowise\"\n    | \"groq\"\n    | \"continue-proxy\"\n    | \"fireworks\"\n    | \"custom\"\n    | \"cloudflare\"\n    | \"deepseek\"\n    | \"azure\"\n    | \"openai-aiohttp\"\n    | \"msty\"\n    | \"watsonx\"\n    | \"openrouter\"\n    | \"nvidia\";\n\n  export type ModelName =\n    | \"AUTODETECT\"\n    // OpenAI\n    | \"gpt-3.5-turbo\"\n    | \"gpt-3.5-turbo-16k\"\n    | \"gpt-4\"\n    | \"gpt-3.5-turbo-0613\"\n    | \"gpt-4-32k\"\n    | \"gpt-4o\"\n    | \"gpt-4o-mini\"\n    | \"gpt-4-turbo\"\n    | \"gpt-4-turbo-preview\"\n    | \"gpt-4-vision-preview\"\n    // Mistral\n    | \"codestral-latest\"\n    | \"open-mistral-7b\"\n    | \"open-mixtral-8x7b\"\n    | \"open-mixtral-8x22b\"\n    | \"mistral-small-latest\"\n    | \"mistral-large-latest\"\n    | \"mistral-7b\"\n    | \"mistral-8x7b\"\n    // Llama 2\n    | \"llama2-7b\"\n    | \"llama2-13b\"\n    | \"llama2-70b\"\n    | \"codellama-7b\"\n    | \"codellama-13b\"\n    | \"codellama-34b\"\n    | \"codellama-70b\"\n    // Llama 3\n    | \"llama3-8b\"\n    | \"llama3-70b\"\n    // Other Open-source\n    | \"phi2\"\n    | \"phind-codellama-34b\"\n    | \"wizardcoder-7b\"\n    | \"wizardcoder-13b\"\n    | \"wizardcoder-34b\"\n    | \"zephyr-7b\"\n    | \"codeup-13b\"\n    | \"deepseek-7b\"\n    | \"deepseek-33b\"\n    | \"neural-chat-7b\"\n    // Anthropic\n    | \"claude-3-5-sonnet-20240620\"\n    | \"claude-3-opus-20240229\"\n    | \"claude-3-sonnet-20240229\"\n    | \"claude-3-haiku-20240307\"\n    | \"claude-2.1\"\n    | \"claude-2\"\n    // Cohere\n    | \"command-r\"\n    | \"command-r-plus\"\n    // Gemini\n    | \"gemini-pro\"\n    | \"gemini-1.5-pro-latest\"\n    | \"gemini-1.5-pro\"\n    | \"gemini-1.5-flash-latest\"\n    | \"gemini-1.5-flash\"\n    // Mistral\n    | \"mistral-tiny\"\n    | \"mistral-small\"\n    | \"mistral-medium\"\n    // Tab autocomplete\n    | \"deepseek-1b\"\n    | \"starcoder-1b\"\n    | \"starcoder-3b\"\n    | \"starcoder2-3b\"\n    | \"stable-code-3b\";\n\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    keepAlive?: number;\n    raw?: boolean;\n    stream?: boolean;\n  }\n\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n\n  export interface ModelDescription {\n    title: string;\n    provider: ModelProvider;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n  }\n\n  export type EmbeddingsProviderName =\n    | \"huggingface-tei\"\n    | \"transformers.js\"\n    | \"ollama\"\n    | \"openai\"\n    | \"cohere\"\n    | \"free-trial\"\n    | \"gemini\"\n    | \"continue-proxy\"\n    | \"deepinfra\"\n    | \"voyage\";\n\n  export interface EmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    engine?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n  }\n\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: EmbeddingsProviderName;\n  }\n\n  export interface EmbeddingsProvider {\n    id: string;\n    providerName: EmbeddingsProviderName;\n    maxChunkSize: number;\n    embed(chunks: string[]): Promise<number[][]>;\n  }\n\n  export type RerankerName =\n    | \"cohere\"\n    | \"voyage\"\n    | \"llm\"\n    | \"free-trial\"\n    | \"huggingface-tei\"\n    | \"continue-proxy\";\n\n  export interface RerankerDescription {\n    name: RerankerName;\n    params?: { [key: string]: any };\n  }\n\n  export interface Reranker {\n    name: string;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  }\n\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    useCopyBuffer: boolean;\n    useFileSuffix: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    maxSnippetPercentage: number;\n    recentlyEditedSimilarityThreshold: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useOtherFiles: boolean;\n    useRecentlyEdited: boolean;\n    recentLinePrefixMatchMinLength: number;\n    disableInFiles?: string[];\n    useImports?: boolean;\n  }\n\n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n  }\n\n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n\n  interface ModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n  }\n\n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n\n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n\n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n\n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n\n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n\n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n\n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  }\n\n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n\n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n\n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n\n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | EmbeddingsProvider;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | Reranker;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n\n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: EmbeddingsProvider;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: Reranker;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n  }\n\n}\n\nexport {};\n\n```\n\nThis is the currently open file:\n\n```.monica-code/types/core/index.d.ts\n\n```\n\n/read-large-file index.d.ts \n\n<assistant>\n# TypeScript Declaration File Analysis: Core Type System\n\nI've analyzed the provided TypeScript declaration file (`index.d.ts`), which defines a comprehensive type system for what appears to be an AI coding assistant named \"Continue\" (based on numerous references throughout the file).\n\n## File Overview\n\nThis is a global declaration file that defines the core type system for the application, making these types available throughout the codebase without explicit imports. The file establishes a robust foundation for an AI-powered coding assistant with extensive IDE integration.\n\n## Key Components\n\n### 1. LLM (Large Language Model) System\n- `ILLM` interface defines core LLM functionality including:\n  - Methods for text completion, chat, token counting\n  - Streaming capabilities\n  - Support for numerous model providers (OpenAI, Anthropic, Cohere, etc.)\n- Comprehensive configuration options via `LLMOptions`, `CompletionOptions`, etc.\n- Support for customizing model behavior through templates and system messages\n\n### 2. Context Provider System\n- `IContextProvider` and related interfaces create a pluggable system for providing context\n- Context items can be retrieved from various sources (codebase, terminal, problems, etc.)\n- Custom context providers can be implemented\n\n### 3. IDE Integration\n- Extensive `IDE` interface for interacting with editors like VSCode and JetBrains\n- File operations (read, write, diff, stat)\n- Editor state (selections, open files, problems)\n- Command execution and UI interactions\n\n### 4. Chat System\n- `ChatMessage`, `ChatHistory` interfaces for conversation management\n- Support for multi-modal content (text + images)\n- Different message roles (user, assistant, system)\n\n### 5. Configuration System\n- Multiple configuration interfaces (`ContinueConfig`, `SerializedContinueConfig`)\n- Configuration for models, providers, UI options, etc.\n- Merge behavior for combining different configuration sources\n\n### 6. Advanced Features\n- Slash commands system for custom commands\n- Embeddings and reranking for semantic search\n- Tab autocompletion system\n- Code chunking for analysis\n- Experimental features (context menu prompts, quick actions)\n\n## Architecture Insights\n\nThe type system reveals a well-structured application with clear separation of concerns:\n- Model interaction is abstracted through interfaces\n- IDE integration is comprehensive but modular\n- Plugin-like systems for context providers and slash commands\n- Configuration is flexible and extensible\n\nThis appears to be a sophisticated AI coding assistant tool that can integrate with development environments and provide contextual assistance using various LLM providers.\n\n<user>\n```.monica-code/AGENT-ANALYSIS.md\n\n```\n\n```.monica-code/README.md\n\n```\n\n```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```\n\nThis is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```\n\nlee estoa 4 archivos a la vez\n\n", "completion": "Voy a leer los cuatro archivos solicitados:", "completionOptions": {"model": "claude-3-7-sonnet-latest-thinking", "maxTokens": 4096, "tools": [{"type": "function", "function": {"name": "builtin_read_file", "description": "Use this tool whenever you need to view the contents of a file.", "parameters": {"type": "object", "required": ["filepath"], "properties": {"filepath": {"type": "string", "description": "The path of the file to read, relative to the root of the workspace."}}}}}, {"type": "function", "function": {"name": "builtin_create_new_file", "description": "Create a new file", "parameters": {"type": "object", "required": ["filepath", "contents"], "properties": {"filepath": {"type": "string", "description": "The path where the new file should be created"}, "contents": {"type": "string", "description": "The contents to write to the new file"}}}}}, {"type": "function", "function": {"name": "builtin_run_terminal_command", "description": "Run a terminal command in the current directory. The shell is not stateful and will not remember any previous commands.", "parameters": {"type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The command to run. This will be passed directly into the shell."}}}}}, {"type": "function", "function": {"name": "builtin_view_subdirectory", "description": "View the contents of a subdirectory", "parameters": {"type": "object", "required": ["directory_path"], "properties": {"directory_path": {"type": "string", "description": "The path of the subdirectory to view, relative to the root of the workspace"}}}}}, {"type": "function", "function": {"name": "builtin_view_repo_map", "description": "View the repository map", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_exact_search", "description": "Perform an exact file contents search over the repository using grep.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query to use. Must be a valid ripgrep regex expression, escaped where needed"}}}}}, {"type": "function", "function": {"name": "builtin_search_web", "description": "Performs a web search, returning top results. This tool should only be called for questions that require external knowledge. Common programming questions do not require web search.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The natural language search query"}}}}}, {"type": "function", "function": {"name": "builtin_view_diff", "description": "View the current diff of working changes", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_read_currently_open_file", "description": "Read the currently open file in the IDE. If the user seems to be referring to a file that you can't see, this is probably it.", "parameters": {"type": "object", "properties": {}}}}]}}], "modelName": "Claude 3.7 Sonnet Thinking", "reasoningContent": "El usuario quiere que lea cuatro archivos diferentes a la vez. Los archivos son:\n\n1. `.monica-code/AGENT-ANALYSIS.md`\n2. `.monica-code/README.md`\n3. `.monica-code/LARGE-FILE-HANDLING-GUIDE.md`\n4. `.monica-code/AUTONOMOUS-AGENT-GUIDE.md` (actualmente abierto)\n\nUtilizaré la función `builtin_read_file` para leer cada uno de estos archivos en secuencia.", "id": "50e9ccc5-c08d-435f-b798-93669c96966e", "content": "Voy a leer los cuatro archivos solicitados:", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065505513, "parentId": "68a7d992-82fc-4350-8d33-f07a2c66894d", "role": "assistant", "toolCalls": [{"id": "tooluse_J1Fk4NzDQfuFKv3yOsgLWQ", "type": "function", "function": {"name": "builtin_read_file", "arguments": "{\"filepath\": \".monica-code/AGENT-ANALYSIS.md\"}"}}]}, {"id": "bd84222a-5065-48ef-837e-aea71774d3a0", "content": "Could not find file .monica-code/AGENT-ANALYSIS.md", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065510561, "parentId": "50e9ccc5-c08d-435f-b798-93669c96966e", "role": "tool", "toolCallId": "tooluse_J1Fk4NzDQfuFKv3yOsgLWQ", "toolName": "builtin_read_file", "parsedArgs": {"filepath": ".monica-code/AGENT-ANALYSIS.md"}, "toolInput": "{\"filepath\": \".monica-code/AGENT-ANALYSIS.md\"}", "status": "error", "toolOutput": [{"id": {"providerTitle": "1", "itemId": "1"}, "name": "Tool Failed", "description": "Tool Failed", "content": "The tool call was failed. Please try something else or request further instructions.", "hidden": true}], "toolError": "Could not find file .monica-code/AGENT-ANALYSIS.md"}, {"codeblockIds": [], "contextResults": [{"name": "AGENT-ANALYSIS.md", "description": ".monica-code/AGENT-ANALYSIS.md", "content": "```.monica-code/AGENT-ANALYSIS.md\n\n```", "uri": {"type": "file", "value": ".monica-code/AGENT-ANALYSIS.md"}, "id": {"providerTitle": "file", "itemId": "b6d6a1ff-28a4-435d-bac6-908fdbf9aafa"}}, {"name": "README.md", "description": ".monica-code/README.md", "content": "```.monica-code/README.md\n\n```", "uri": {"type": "file", "value": ".monica-code/README.md"}, "id": {"providerTitle": "file", "itemId": "c1311f13-17c7-4fa2-9f01-305f222ff20a"}}, {"name": "LARGE-FILE-HANDLING-GUIDE.md", "description": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md", "content": "```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```", "uri": {"type": "file", "value": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, "id": {"providerTitle": "file", "itemId": "e9be3536-14ae-4b7c-bc41-e714b5fde578"}}, {"description": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md", "content": "This is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```", "name": "AUTONOMOUS-AGENT-GUIDE.md", "uri": {"type": "file", "value": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, "id": {"providerTitle": "currentFile", "itemId": "03944af8-b330-460e-9a37-c5da32b3ded0"}}], "isComplete": true, "promptLogs": [{"modelTitle": "Claude 3.7 Sonnet Thinking", "prompt": "<system>\nWhen you need to edit or create new files, use the format ```lang:path at the start of a code block. For example:\n\n  - To edit an existing file: ```ts:path/to/edit.ts\n  - To create a new file: ```py:path/to/new/file.py\n  \n  The path can be either:\n  - Absolute (e.g., /Users/<USER>/project/file.js)\n  - Relative (e.g., src/components/Button.tsx, starting WITHOUT '/')\n  \n  The path must be complete and clear. When providing edits instead of the complete file, include clear comments explaining the changes.\n\n  Always respond in the same language that the user writes in. If the user messages in English, respond in English; if in Chinese, respond in Chinese; and so forth. Language matching is crucial for effective communication.\n\nEres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones.\nCurrent Date is: 2025-06-04T19:31:51.160Z\n\n<user>\n```c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts\n\ndeclare global {\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status: \"loading\" | \"indexing\" | \"done\" | \"failed\" | \"paused\" | \"disabled\";\n    debugInfo?: string;\n  }\n\n  export type PromptTemplate =\n    | string\n    | ((\n        history: ChatMessage[],\n        otherData: Record<string, string>,\n      ) => string | ChatMessage[]);\n\n  export interface ILLM extends LLMOptions {\n    get providerName(): ModelProvider;\n\n    uniqueId: string;\n    model: string;\n\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n\n    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;\n\n    streamComplete(\n      prompt: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamFim(\n      prefix: string,\n      suffix: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamChat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n\n    chat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n\n    countTokens(text: string): number;\n\n    supportsImages(): boolean;\n\n    supportsCompletions(): boolean;\n\n    supportsPrefill(): boolean;\n\n    supportsFim(): boolean;\n\n    listModels(): Promise<string[]>;\n\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n\n  export interface ContextProviderDescription {\n    title: string;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n  }\n\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: EmbeddingsProvider;\n    reranker: Reranker | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    rootUrl?: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n\n  export interface SiteIndexingConfig {\n    startUrl: string;\n    rootUrl?: string;\n    title: string;\n    maxDepth?: number;\n  }\n\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface PersistedSessionInfo {\n    history: ChatHistory;\n    title: string;\n    workspaceDirectory: string;\n    sessionId: string;\n  }\n\n  export interface SessionInfo {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n\n  export interface RangeInFile {\n    filepath: string;\n    content?: string\n    range: Range;\n  }\n\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\";\n\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n\n  export type MessageContent = string | MessagePart[];\n\n  export interface ChatMessage {\n    role: ChatMessageRole;\n    content: MessageContent;\n  }\n\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface ContextItemWithId {\n    content: string;\n    name: string;\n    description: string;\n    id: ContextItemId;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    editorState?: any;\n    modifiers?: InputModifiers;\n    contextItems: ContextItemWithId[];\n    promptLogs?: PromptLog[];\n  }\n\n  export type ChatHistory = ChatHistoryItem[];\n\n  // LLM\n\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n\n    model?: string;\n  }\n  export interface LLMOptions {\n    model: string;\n\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n\n    useLegacyCompletionsEndpoint?: boolean;\n\n    // Cloudflare options\n    accountId?: string;\n\n    // Azure options\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n\n    // AWS options\n    profile?: string;\n    modelArn?: string;\n\n    // AWS and GCP Options\n    region?: string;\n\n    // GCP Options\n    projectId?: string;\n    capabilities?: ModelCapability;\n\n    // IBM watsonx options\n    watsonxUrl?: string;\n    watsonxCreds?: string;\n    watsonxProjectId?: string;\n    watsonxStopToken?: string;\n    watsonxApiVersion?: string;\n\n    cacheSystemMessage?: boolean;\n  }\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n\n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n\n  // IDE\n\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n\n  export class Thread {\n    name: string;\n    id: number;\n  }\n\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    enableControlServerBeta: boolean;\n    pauseCodebaseIndexOnStart: boolean;\n    enableDebugLogs: boolean;\n  }\n\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(): Promise<string>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    listFolders(): Promise<string[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileStat(filepath: string): Promise<{exists: boolean, type: number}>;\n    readRawFile(filepath: string): Promise<string>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    getContinueDir(): Promise<string>;\n    openFile(path: string): Promise<void>;\n    runCommand(command: string, shouldExecute?: boolean): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    showDiff(\n      filepath: string,\n      newContents: string,\n      stepIndex: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getOpenEditors(): Promise<string[]>;\n    getCurrentFile(): Promise<string | undefined>;\n    getCurrentFileSelection(): Promise<RangeInFile | undefined>;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    errorPopup(message: string): Promise<void>;\n    infoPopup(message: string): Promise<void>;\n\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    getGitHubAuthToken(): Promise<string | undefined>;\n\n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n\n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n    onDidChangeActiveTextSelection(callback: (selection?: Range) => void): void\n    pathSep(): Promise<string>;\n  }\n\n  // Slash Commands\n\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n\n  // Config\n\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n\n  type ContextProviderName =\n    | \"diff\"\n    | \"github\"\n    | \"terminal\"\n    | \"locals\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\";\n\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"llama3\";\n\n  type ModelProvider =\n    | \"openai\"\n    | \"free-trial\"\n    | \"anthropic\"\n    | \"cohere\"\n    | \"together\"\n    | \"ollama\"\n    | \"huggingface-tgi\"\n    | \"huggingface-inference-api\"\n    | \"kindo\"\n    | \"llama.cpp\"\n    | \"replicate\"\n    | \"text-gen-webui\"\n    | \"lmstudio\"\n    | \"llamafile\"\n    | \"gemini\"\n    | \"mistral\"\n    | \"bedrock\"\n    | \"bedrockimport\"\n    | \"sagemaker\"\n    | \"deepinfra\"\n    | \"flowise\"\n    | \"groq\"\n    | \"continue-proxy\"\n    | \"fireworks\"\n    | \"custom\"\n    | \"cloudflare\"\n    | \"deepseek\"\n    | \"azure\"\n    | \"openai-aiohttp\"\n    | \"msty\"\n    | \"watsonx\"\n    | \"openrouter\"\n    | \"nvidia\";\n\n  export type ModelName =\n    | \"AUTODETECT\"\n    // OpenAI\n    | \"gpt-3.5-turbo\"\n    | \"gpt-3.5-turbo-16k\"\n    | \"gpt-4\"\n    | \"gpt-3.5-turbo-0613\"\n    | \"gpt-4-32k\"\n    | \"gpt-4o\"\n    | \"gpt-4o-mini\"\n    | \"gpt-4-turbo\"\n    | \"gpt-4-turbo-preview\"\n    | \"gpt-4-vision-preview\"\n    // Mistral\n    | \"codestral-latest\"\n    | \"open-mistral-7b\"\n    | \"open-mixtral-8x7b\"\n    | \"open-mixtral-8x22b\"\n    | \"mistral-small-latest\"\n    | \"mistral-large-latest\"\n    | \"mistral-7b\"\n    | \"mistral-8x7b\"\n    // Llama 2\n    | \"llama2-7b\"\n    | \"llama2-13b\"\n    | \"llama2-70b\"\n    | \"codellama-7b\"\n    | \"codellama-13b\"\n    | \"codellama-34b\"\n    | \"codellama-70b\"\n    // Llama 3\n    | \"llama3-8b\"\n    | \"llama3-70b\"\n    // Other Open-source\n    | \"phi2\"\n    | \"phind-codellama-34b\"\n    | \"wizardcoder-7b\"\n    | \"wizardcoder-13b\"\n    | \"wizardcoder-34b\"\n    | \"zephyr-7b\"\n    | \"codeup-13b\"\n    | \"deepseek-7b\"\n    | \"deepseek-33b\"\n    | \"neural-chat-7b\"\n    // Anthropic\n    | \"claude-3-5-sonnet-20240620\"\n    | \"claude-3-opus-20240229\"\n    | \"claude-3-sonnet-20240229\"\n    | \"claude-3-haiku-20240307\"\n    | \"claude-2.1\"\n    | \"claude-2\"\n    // Cohere\n    | \"command-r\"\n    | \"command-r-plus\"\n    // Gemini\n    | \"gemini-pro\"\n    | \"gemini-1.5-pro-latest\"\n    | \"gemini-1.5-pro\"\n    | \"gemini-1.5-flash-latest\"\n    | \"gemini-1.5-flash\"\n    // Mistral\n    | \"mistral-tiny\"\n    | \"mistral-small\"\n    | \"mistral-medium\"\n    // Tab autocomplete\n    | \"deepseek-1b\"\n    | \"starcoder-1b\"\n    | \"starcoder-3b\"\n    | \"starcoder2-3b\"\n    | \"stable-code-3b\";\n\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    keepAlive?: number;\n    raw?: boolean;\n    stream?: boolean;\n  }\n\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n\n  export interface ModelDescription {\n    title: string;\n    provider: ModelProvider;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n  }\n\n  export type EmbeddingsProviderName =\n    | \"huggingface-tei\"\n    | \"transformers.js\"\n    | \"ollama\"\n    | \"openai\"\n    | \"cohere\"\n    | \"free-trial\"\n    | \"gemini\"\n    | \"continue-proxy\"\n    | \"deepinfra\"\n    | \"voyage\";\n\n  export interface EmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    engine?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n  }\n\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: EmbeddingsProviderName;\n  }\n\n  export interface EmbeddingsProvider {\n    id: string;\n    providerName: EmbeddingsProviderName;\n    maxChunkSize: number;\n    embed(chunks: string[]): Promise<number[][]>;\n  }\n\n  export type RerankerName =\n    | \"cohere\"\n    | \"voyage\"\n    | \"llm\"\n    | \"free-trial\"\n    | \"huggingface-tei\"\n    | \"continue-proxy\";\n\n  export interface RerankerDescription {\n    name: RerankerName;\n    params?: { [key: string]: any };\n  }\n\n  export interface Reranker {\n    name: string;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  }\n\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    useCopyBuffer: boolean;\n    useFileSuffix: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    maxSnippetPercentage: number;\n    recentlyEditedSimilarityThreshold: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useOtherFiles: boolean;\n    useRecentlyEdited: boolean;\n    recentLinePrefixMatchMinLength: number;\n    disableInFiles?: string[];\n    useImports?: boolean;\n  }\n\n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n  }\n\n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n\n  interface ModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n  }\n\n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n\n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n\n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n\n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n\n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n\n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n\n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  }\n\n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n\n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n\n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n\n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | EmbeddingsProvider;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | Reranker;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n\n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: EmbeddingsProvider;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: Reranker;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n  }\n\n}\n\nexport {};\n\n```\n\nThis is the currently open file:\n\n```.monica-code/types/core/index.d.ts\n\n```\n\n/read-large-file index.d.ts \n\n<assistant>\n# TypeScript Declaration File Analysis: Core Type System\n\nI've analyzed the provided TypeScript declaration file (`index.d.ts`), which defines a comprehensive type system for what appears to be an AI coding assistant named \"Continue\" (based on numerous references throughout the file).\n\n## File Overview\n\nThis is a global declaration file that defines the core type system for the application, making these types available throughout the codebase without explicit imports. The file establishes a robust foundation for an AI-powered coding assistant with extensive IDE integration.\n\n## Key Components\n\n### 1. LLM (Large Language Model) System\n- `ILLM` interface defines core LLM functionality including:\n  - Methods for text completion, chat, token counting\n  - Streaming capabilities\n  - Support for numerous model providers (OpenAI, Anthropic, Cohere, etc.)\n- Comprehensive configuration options via `LLMOptions`, `CompletionOptions`, etc.\n- Support for customizing model behavior through templates and system messages\n\n### 2. Context Provider System\n- `IContextProvider` and related interfaces create a pluggable system for providing context\n- Context items can be retrieved from various sources (codebase, terminal, problems, etc.)\n- Custom context providers can be implemented\n\n### 3. IDE Integration\n- Extensive `IDE` interface for interacting with editors like VSCode and JetBrains\n- File operations (read, write, diff, stat)\n- Editor state (selections, open files, problems)\n- Command execution and UI interactions\n\n### 4. Chat System\n- `ChatMessage`, `ChatHistory` interfaces for conversation management\n- Support for multi-modal content (text + images)\n- Different message roles (user, assistant, system)\n\n### 5. Configuration System\n- Multiple configuration interfaces (`ContinueConfig`, `SerializedContinueConfig`)\n- Configuration for models, providers, UI options, etc.\n- Merge behavior for combining different configuration sources\n\n### 6. Advanced Features\n- Slash commands system for custom commands\n- Embeddings and reranking for semantic search\n- Tab autocompletion system\n- Code chunking for analysis\n- Experimental features (context menu prompts, quick actions)\n\n## Architecture Insights\n\nThe type system reveals a well-structured application with clear separation of concerns:\n- Model interaction is abstracted through interfaces\n- IDE integration is comprehensive but modular\n- Plugin-like systems for context providers and slash commands\n- Configuration is flexible and extensible\n\nThis appears to be a sophisticated AI coding assistant tool that can integrate with development environments and provide contextual assistance using various LLM providers.\n\n<user>\n```.monica-code/AGENT-ANALYSIS.md\n\n```\n\n```.monica-code/README.md\n\n```\n\n```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```\n\nThis is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```\n\nlee estoa 4 archivos a la vez\n\n<assistant>\nVoy a leer los cuatro archivos solicitados:\n\n<tool>\nCould not find file .monica-code/AGENT-ANALYSIS.md\n\n", "completion": "Veo que el primer archivo `.monica-code/AGENT-ANALYSIS.md` no se encuentra. Continuaré con los demás archivos de la lista:", "completionOptions": {"model": "claude-3-7-sonnet-latest-thinking", "maxTokens": 4096, "tools": [{"type": "function", "function": {"name": "builtin_read_file", "description": "Use this tool whenever you need to view the contents of a file.", "parameters": {"type": "object", "required": ["filepath"], "properties": {"filepath": {"type": "string", "description": "The path of the file to read, relative to the root of the workspace."}}}}}, {"type": "function", "function": {"name": "builtin_create_new_file", "description": "Create a new file", "parameters": {"type": "object", "required": ["filepath", "contents"], "properties": {"filepath": {"type": "string", "description": "The path where the new file should be created"}, "contents": {"type": "string", "description": "The contents to write to the new file"}}}}}, {"type": "function", "function": {"name": "builtin_run_terminal_command", "description": "Run a terminal command in the current directory. The shell is not stateful and will not remember any previous commands.", "parameters": {"type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The command to run. This will be passed directly into the shell."}}}}}, {"type": "function", "function": {"name": "builtin_view_subdirectory", "description": "View the contents of a subdirectory", "parameters": {"type": "object", "required": ["directory_path"], "properties": {"directory_path": {"type": "string", "description": "The path of the subdirectory to view, relative to the root of the workspace"}}}}}, {"type": "function", "function": {"name": "builtin_view_repo_map", "description": "View the repository map", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_exact_search", "description": "Perform an exact file contents search over the repository using grep.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query to use. Must be a valid ripgrep regex expression, escaped where needed"}}}}}, {"type": "function", "function": {"name": "builtin_search_web", "description": "Performs a web search, returning top results. This tool should only be called for questions that require external knowledge. Common programming questions do not require web search.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The natural language search query"}}}}}, {"type": "function", "function": {"name": "builtin_view_diff", "description": "View the current diff of working changes", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_read_currently_open_file", "description": "Read the currently open file in the IDE. If the user seems to be referring to a file that you can't see, this is probably it.", "parameters": {"type": "object", "properties": {}}}}]}}], "modelName": "Claude 3.7 Sonnet Thinking", "reasoningContent": "Parece que el archivo `.monica-code/AGENT-ANALYSIS.md` no existe. Vamos a intentar con los otros archivos de la lista.", "id": "30cfdc44-3c04-48c4-a215-1f011fcb76f9", "content": "Veo que el primer archivo `.monica-code/AGENT-ANALYSIS.md` no se encuentra. Continuaré con los demás archivos de la lista:", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065511137, "parentId": "bd84222a-5065-48ef-837e-aea71774d3a0", "role": "assistant", "toolCalls": [{"id": "tooluse_guGFsA2GTY6LQrkWBObPpg", "type": "function", "function": {"name": "builtin_read_file", "arguments": "{\"filepath\": \".monica-code/README.md\"}"}}]}, {"id": "3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d", "content": "Could not find file .monica-code/README.md", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065516417, "parentId": "30cfdc44-3c04-48c4-a215-1f011fcb76f9", "role": "tool", "toolCallId": "tooluse_guGFsA2GTY6LQrkWBObPpg", "toolName": "builtin_read_file", "parsedArgs": {"filepath": ".monica-code/README.md"}, "toolInput": "{\"filepath\": \".monica-code/README.md\"}", "status": "error", "toolOutput": [{"id": {"providerTitle": "1", "itemId": "1"}, "name": "Tool Failed", "description": "Tool Failed", "content": "The tool call was failed. Please try something else or request further instructions.", "hidden": true}], "toolError": "Could not find file .monica-code/README.md"}, {"codeblockIds": [], "contextResults": [{"name": "AGENT-ANALYSIS.md", "description": ".monica-code/AGENT-ANALYSIS.md", "content": "```.monica-code/AGENT-ANALYSIS.md\n\n```", "uri": {"type": "file", "value": ".monica-code/AGENT-ANALYSIS.md"}, "id": {"providerTitle": "file", "itemId": "0a747ee2-12be-44da-9262-5a49954392ca"}}, {"name": "README.md", "description": ".monica-code/README.md", "content": "```.monica-code/README.md\n\n```", "uri": {"type": "file", "value": ".monica-code/README.md"}, "id": {"providerTitle": "file", "itemId": "117e8d86-668e-4bb4-8a5f-abb12152992b"}}, {"name": "LARGE-FILE-HANDLING-GUIDE.md", "description": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md", "content": "```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```", "uri": {"type": "file", "value": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, "id": {"providerTitle": "file", "itemId": "f8d42640-ec8a-4c7f-a206-1acb44a92d6e"}}, {"description": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md", "content": "This is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```", "name": "AUTONOMOUS-AGENT-GUIDE.md", "uri": {"type": "file", "value": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, "id": {"providerTitle": "currentFile", "itemId": "f26bc043-f621-414d-aa50-07e630537854"}}], "isComplete": true, "promptLogs": [{"modelTitle": "Claude 3.7 Sonnet Thinking", "prompt": "<system>\nWhen you need to edit or create new files, use the format ```lang:path at the start of a code block. For example:\n\n  - To edit an existing file: ```ts:path/to/edit.ts\n  - To create a new file: ```py:path/to/new/file.py\n  \n  The path can be either:\n  - Absolute (e.g., /Users/<USER>/project/file.js)\n  - Relative (e.g., src/components/Button.tsx, starting WITHOUT '/')\n  \n  The path must be complete and clear. When providing edits instead of the complete file, include clear comments explaining the changes.\n\n  Always respond in the same language that the user writes in. If the user messages in English, respond in English; if in Chinese, respond in Chinese; and so forth. Language matching is crucial for effective communication.\n\nEres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones.\nCurrent Date is: 2025-06-04T19:31:57.173Z\n\n<user>\n```c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts\n\ndeclare global {\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status: \"loading\" | \"indexing\" | \"done\" | \"failed\" | \"paused\" | \"disabled\";\n    debugInfo?: string;\n  }\n\n  export type PromptTemplate =\n    | string\n    | ((\n        history: ChatMessage[],\n        otherData: Record<string, string>,\n      ) => string | ChatMessage[]);\n\n  export interface ILLM extends LLMOptions {\n    get providerName(): ModelProvider;\n\n    uniqueId: string;\n    model: string;\n\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n\n    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;\n\n    streamComplete(\n      prompt: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamFim(\n      prefix: string,\n      suffix: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamChat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n\n    chat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n\n    countTokens(text: string): number;\n\n    supportsImages(): boolean;\n\n    supportsCompletions(): boolean;\n\n    supportsPrefill(): boolean;\n\n    supportsFim(): boolean;\n\n    listModels(): Promise<string[]>;\n\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n\n  export interface ContextProviderDescription {\n    title: string;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n  }\n\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: EmbeddingsProvider;\n    reranker: Reranker | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    rootUrl?: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n\n  export interface SiteIndexingConfig {\n    startUrl: string;\n    rootUrl?: string;\n    title: string;\n    maxDepth?: number;\n  }\n\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface PersistedSessionInfo {\n    history: ChatHistory;\n    title: string;\n    workspaceDirectory: string;\n    sessionId: string;\n  }\n\n  export interface SessionInfo {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n\n  export interface RangeInFile {\n    filepath: string;\n    content?: string\n    range: Range;\n  }\n\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\";\n\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n\n  export type MessageContent = string | MessagePart[];\n\n  export interface ChatMessage {\n    role: ChatMessageRole;\n    content: MessageContent;\n  }\n\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface ContextItemWithId {\n    content: string;\n    name: string;\n    description: string;\n    id: ContextItemId;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    editorState?: any;\n    modifiers?: InputModifiers;\n    contextItems: ContextItemWithId[];\n    promptLogs?: PromptLog[];\n  }\n\n  export type ChatHistory = ChatHistoryItem[];\n\n  // LLM\n\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n\n    model?: string;\n  }\n  export interface LLMOptions {\n    model: string;\n\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n\n    useLegacyCompletionsEndpoint?: boolean;\n\n    // Cloudflare options\n    accountId?: string;\n\n    // Azure options\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n\n    // AWS options\n    profile?: string;\n    modelArn?: string;\n\n    // AWS and GCP Options\n    region?: string;\n\n    // GCP Options\n    projectId?: string;\n    capabilities?: ModelCapability;\n\n    // IBM watsonx options\n    watsonxUrl?: string;\n    watsonxCreds?: string;\n    watsonxProjectId?: string;\n    watsonxStopToken?: string;\n    watsonxApiVersion?: string;\n\n    cacheSystemMessage?: boolean;\n  }\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n\n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n\n  // IDE\n\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n\n  export class Thread {\n    name: string;\n    id: number;\n  }\n\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    enableControlServerBeta: boolean;\n    pauseCodebaseIndexOnStart: boolean;\n    enableDebugLogs: boolean;\n  }\n\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(): Promise<string>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    listFolders(): Promise<string[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileStat(filepath: string): Promise<{exists: boolean, type: number}>;\n    readRawFile(filepath: string): Promise<string>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    getContinueDir(): Promise<string>;\n    openFile(path: string): Promise<void>;\n    runCommand(command: string, shouldExecute?: boolean): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    showDiff(\n      filepath: string,\n      newContents: string,\n      stepIndex: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getOpenEditors(): Promise<string[]>;\n    getCurrentFile(): Promise<string | undefined>;\n    getCurrentFileSelection(): Promise<RangeInFile | undefined>;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    errorPopup(message: string): Promise<void>;\n    infoPopup(message: string): Promise<void>;\n\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    getGitHubAuthToken(): Promise<string | undefined>;\n\n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n\n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n    onDidChangeActiveTextSelection(callback: (selection?: Range) => void): void\n    pathSep(): Promise<string>;\n  }\n\n  // Slash Commands\n\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n\n  // Config\n\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n\n  type ContextProviderName =\n    | \"diff\"\n    | \"github\"\n    | \"terminal\"\n    | \"locals\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\";\n\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"llama3\";\n\n  type ModelProvider =\n    | \"openai\"\n    | \"free-trial\"\n    | \"anthropic\"\n    | \"cohere\"\n    | \"together\"\n    | \"ollama\"\n    | \"huggingface-tgi\"\n    | \"huggingface-inference-api\"\n    | \"kindo\"\n    | \"llama.cpp\"\n    | \"replicate\"\n    | \"text-gen-webui\"\n    | \"lmstudio\"\n    | \"llamafile\"\n    | \"gemini\"\n    | \"mistral\"\n    | \"bedrock\"\n    | \"bedrockimport\"\n    | \"sagemaker\"\n    | \"deepinfra\"\n    | \"flowise\"\n    | \"groq\"\n    | \"continue-proxy\"\n    | \"fireworks\"\n    | \"custom\"\n    | \"cloudflare\"\n    | \"deepseek\"\n    | \"azure\"\n    | \"openai-aiohttp\"\n    | \"msty\"\n    | \"watsonx\"\n    | \"openrouter\"\n    | \"nvidia\";\n\n  export type ModelName =\n    | \"AUTODETECT\"\n    // OpenAI\n    | \"gpt-3.5-turbo\"\n    | \"gpt-3.5-turbo-16k\"\n    | \"gpt-4\"\n    | \"gpt-3.5-turbo-0613\"\n    | \"gpt-4-32k\"\n    | \"gpt-4o\"\n    | \"gpt-4o-mini\"\n    | \"gpt-4-turbo\"\n    | \"gpt-4-turbo-preview\"\n    | \"gpt-4-vision-preview\"\n    // Mistral\n    | \"codestral-latest\"\n    | \"open-mistral-7b\"\n    | \"open-mixtral-8x7b\"\n    | \"open-mixtral-8x22b\"\n    | \"mistral-small-latest\"\n    | \"mistral-large-latest\"\n    | \"mistral-7b\"\n    | \"mistral-8x7b\"\n    // Llama 2\n    | \"llama2-7b\"\n    | \"llama2-13b\"\n    | \"llama2-70b\"\n    | \"codellama-7b\"\n    | \"codellama-13b\"\n    | \"codellama-34b\"\n    | \"codellama-70b\"\n    // Llama 3\n    | \"llama3-8b\"\n    | \"llama3-70b\"\n    // Other Open-source\n    | \"phi2\"\n    | \"phind-codellama-34b\"\n    | \"wizardcoder-7b\"\n    | \"wizardcoder-13b\"\n    | \"wizardcoder-34b\"\n    | \"zephyr-7b\"\n    | \"codeup-13b\"\n    | \"deepseek-7b\"\n    | \"deepseek-33b\"\n    | \"neural-chat-7b\"\n    // Anthropic\n    | \"claude-3-5-sonnet-20240620\"\n    | \"claude-3-opus-20240229\"\n    | \"claude-3-sonnet-20240229\"\n    | \"claude-3-haiku-20240307\"\n    | \"claude-2.1\"\n    | \"claude-2\"\n    // Cohere\n    | \"command-r\"\n    | \"command-r-plus\"\n    // Gemini\n    | \"gemini-pro\"\n    | \"gemini-1.5-pro-latest\"\n    | \"gemini-1.5-pro\"\n    | \"gemini-1.5-flash-latest\"\n    | \"gemini-1.5-flash\"\n    // Mistral\n    | \"mistral-tiny\"\n    | \"mistral-small\"\n    | \"mistral-medium\"\n    // Tab autocomplete\n    | \"deepseek-1b\"\n    | \"starcoder-1b\"\n    | \"starcoder-3b\"\n    | \"starcoder2-3b\"\n    | \"stable-code-3b\";\n\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    keepAlive?: number;\n    raw?: boolean;\n    stream?: boolean;\n  }\n\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n\n  export interface ModelDescription {\n    title: string;\n    provider: ModelProvider;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n  }\n\n  export type EmbeddingsProviderName =\n    | \"huggingface-tei\"\n    | \"transformers.js\"\n    | \"ollama\"\n    | \"openai\"\n    | \"cohere\"\n    | \"free-trial\"\n    | \"gemini\"\n    | \"continue-proxy\"\n    | \"deepinfra\"\n    | \"voyage\";\n\n  export interface EmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    engine?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n  }\n\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: EmbeddingsProviderName;\n  }\n\n  export interface EmbeddingsProvider {\n    id: string;\n    providerName: EmbeddingsProviderName;\n    maxChunkSize: number;\n    embed(chunks: string[]): Promise<number[][]>;\n  }\n\n  export type RerankerName =\n    | \"cohere\"\n    | \"voyage\"\n    | \"llm\"\n    | \"free-trial\"\n    | \"huggingface-tei\"\n    | \"continue-proxy\";\n\n  export interface RerankerDescription {\n    name: RerankerName;\n    params?: { [key: string]: any };\n  }\n\n  export interface Reranker {\n    name: string;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  }\n\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    useCopyBuffer: boolean;\n    useFileSuffix: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    maxSnippetPercentage: number;\n    recentlyEditedSimilarityThreshold: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useOtherFiles: boolean;\n    useRecentlyEdited: boolean;\n    recentLinePrefixMatchMinLength: number;\n    disableInFiles?: string[];\n    useImports?: boolean;\n  }\n\n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n  }\n\n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n\n  interface ModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n  }\n\n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n\n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n\n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n\n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n\n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n\n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n\n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  }\n\n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n\n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n\n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n\n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | EmbeddingsProvider;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | Reranker;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n\n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: EmbeddingsProvider;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: Reranker;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n  }\n\n}\n\nexport {};\n\n```\n\nThis is the currently open file:\n\n```.monica-code/types/core/index.d.ts\n\n```\n\n/read-large-file index.d.ts \n\n<assistant>\n# TypeScript Declaration File Analysis: Core Type System\n\nI've analyzed the provided TypeScript declaration file (`index.d.ts`), which defines a comprehensive type system for what appears to be an AI coding assistant named \"Continue\" (based on numerous references throughout the file).\n\n## File Overview\n\nThis is a global declaration file that defines the core type system for the application, making these types available throughout the codebase without explicit imports. The file establishes a robust foundation for an AI-powered coding assistant with extensive IDE integration.\n\n## Key Components\n\n### 1. LLM (Large Language Model) System\n- `ILLM` interface defines core LLM functionality including:\n  - Methods for text completion, chat, token counting\n  - Streaming capabilities\n  - Support for numerous model providers (OpenAI, Anthropic, Cohere, etc.)\n- Comprehensive configuration options via `LLMOptions`, `CompletionOptions`, etc.\n- Support for customizing model behavior through templates and system messages\n\n### 2. Context Provider System\n- `IContextProvider` and related interfaces create a pluggable system for providing context\n- Context items can be retrieved from various sources (codebase, terminal, problems, etc.)\n- Custom context providers can be implemented\n\n### 3. IDE Integration\n- Extensive `IDE` interface for interacting with editors like VSCode and JetBrains\n- File operations (read, write, diff, stat)\n- Editor state (selections, open files, problems)\n- Command execution and UI interactions\n\n### 4. Chat System\n- `ChatMessage`, `ChatHistory` interfaces for conversation management\n- Support for multi-modal content (text + images)\n- Different message roles (user, assistant, system)\n\n### 5. Configuration System\n- Multiple configuration interfaces (`ContinueConfig`, `SerializedContinueConfig`)\n- Configuration for models, providers, UI options, etc.\n- Merge behavior for combining different configuration sources\n\n### 6. Advanced Features\n- Slash commands system for custom commands\n- Embeddings and reranking for semantic search\n- Tab autocompletion system\n- Code chunking for analysis\n- Experimental features (context menu prompts, quick actions)\n\n## Architecture Insights\n\nThe type system reveals a well-structured application with clear separation of concerns:\n- Model interaction is abstracted through interfaces\n- IDE integration is comprehensive but modular\n- Plugin-like systems for context providers and slash commands\n- Configuration is flexible and extensible\n\nThis appears to be a sophisticated AI coding assistant tool that can integrate with development environments and provide contextual assistance using various LLM providers.\n\n<user>\n```.monica-code/AGENT-ANALYSIS.md\n\n```\n\n```.monica-code/README.md\n\n```\n\n```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```\n\nThis is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```\n\nlee estoa 4 archivos a la vez\n\n<assistant>\nVoy a leer los cuatro archivos solicitados:\n\n<tool>\nCould not find file .monica-code/AGENT-ANALYSIS.md\n\n<assistant>\nVeo que el primer archivo `.monica-code/AGENT-ANALYSIS.md` no se encuentra. Continuaré con los demás archivos de la lista:\n\n<tool>\nCould not find file .monica-code/README.md\n\n", "completion": "Veo que tampoco se encontró el archivo `.monica-code/README.md`. Continuaré con los otros dos archivos de la lista:", "completionOptions": {"model": "claude-3-7-sonnet-latest-thinking", "maxTokens": 4096, "tools": [{"type": "function", "function": {"name": "builtin_read_file", "description": "Use this tool whenever you need to view the contents of a file.", "parameters": {"type": "object", "required": ["filepath"], "properties": {"filepath": {"type": "string", "description": "The path of the file to read, relative to the root of the workspace."}}}}}, {"type": "function", "function": {"name": "builtin_create_new_file", "description": "Create a new file", "parameters": {"type": "object", "required": ["filepath", "contents"], "properties": {"filepath": {"type": "string", "description": "The path where the new file should be created"}, "contents": {"type": "string", "description": "The contents to write to the new file"}}}}}, {"type": "function", "function": {"name": "builtin_run_terminal_command", "description": "Run a terminal command in the current directory. The shell is not stateful and will not remember any previous commands.", "parameters": {"type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The command to run. This will be passed directly into the shell."}}}}}, {"type": "function", "function": {"name": "builtin_view_subdirectory", "description": "View the contents of a subdirectory", "parameters": {"type": "object", "required": ["directory_path"], "properties": {"directory_path": {"type": "string", "description": "The path of the subdirectory to view, relative to the root of the workspace"}}}}}, {"type": "function", "function": {"name": "builtin_view_repo_map", "description": "View the repository map", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_exact_search", "description": "Perform an exact file contents search over the repository using grep.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query to use. Must be a valid ripgrep regex expression, escaped where needed"}}}}}, {"type": "function", "function": {"name": "builtin_search_web", "description": "Performs a web search, returning top results. This tool should only be called for questions that require external knowledge. Common programming questions do not require web search.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The natural language search query"}}}}}, {"type": "function", "function": {"name": "builtin_view_diff", "description": "View the current diff of working changes", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_read_currently_open_file", "description": "Read the currently open file in the IDE. If the user seems to be referring to a file that you can't see, this is probably it.", "parameters": {"type": "object", "properties": {}}}}]}}], "modelName": "Claude 3.7 Sonnet Thinking", "reasoningContent": "Parece que los archivos `.monica-code/AGENT-ANALYSIS.md` y `.monica-code/README.md` no existen. Voy a intentar con los otros dos archivos de la lista:", "id": "76a339c6-6f3b-469d-9313-8468b33ee1f5", "content": "Veo que tampoco se encontró el archivo `.monica-code/README.md`. Continuaré con los otros dos archivos de la lista:", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065517154, "parentId": "3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d", "role": "assistant", "toolCalls": [{"id": "tooluse_uowQKGSCSV29iVRpwmO94w", "type": "function", "function": {"name": "builtin_read_file", "arguments": "{\"filepath\": \".monica-code/LARGE-FILE-HANDLING-GUIDE.md\"}"}}]}, {"id": "645a354b-2eaf-49a7-b862-ca436c6c45b7", "content": "Could not find file .monica-code/LARGE-FILE-HANDLING-GUIDE.md", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065520878, "parentId": "76a339c6-6f3b-469d-9313-8468b33ee1f5", "role": "tool", "toolCallId": "tooluse_uowQKGSCSV29iVRpwmO94w", "toolName": "builtin_read_file", "parsedArgs": {"filepath": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, "toolInput": "{\"filepath\": \".monica-code/LARGE-FILE-HANDLING-GUIDE.md\"}", "status": "error", "toolOutput": [{"id": {"providerTitle": "1", "itemId": "1"}, "name": "Tool Failed", "description": "Tool Failed", "content": "The tool call was failed. Please try something else or request further instructions.", "hidden": true}], "toolError": "Could not find file .monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, {"codeblockIds": [], "contextResults": [{"name": "AGENT-ANALYSIS.md", "description": ".monica-code/AGENT-ANALYSIS.md", "content": "```.monica-code/AGENT-ANALYSIS.md\n\n```", "uri": {"type": "file", "value": ".monica-code/AGENT-ANALYSIS.md"}, "id": {"providerTitle": "file", "itemId": "2ceb0347-f7b7-461d-a33f-a94ec78d4993"}}, {"name": "README.md", "description": ".monica-code/README.md", "content": "```.monica-code/README.md\n\n```", "uri": {"type": "file", "value": ".monica-code/README.md"}, "id": {"providerTitle": "file", "itemId": "749ad535-baaa-4015-bdda-b208b422e774"}}, {"name": "LARGE-FILE-HANDLING-GUIDE.md", "description": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md", "content": "```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```", "uri": {"type": "file", "value": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, "id": {"providerTitle": "file", "itemId": "d9c59004-5eae-4275-b401-c7516da9e507"}}, {"description": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md", "content": "This is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```", "name": "AUTONOMOUS-AGENT-GUIDE.md", "uri": {"type": "file", "value": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, "id": {"providerTitle": "currentFile", "itemId": "e60d9e8f-d03b-4a78-8b26-9f7c63ffd840"}}], "isComplete": true, "promptLogs": [{"modelTitle": "Claude 3.7 Sonnet Thinking", "prompt": "<system>\nWhen you need to edit or create new files, use the format ```lang:path at the start of a code block. For example:\n\n  - To edit an existing file: ```ts:path/to/edit.ts\n  - To create a new file: ```py:path/to/new/file.py\n  \n  The path can be either:\n  - Absolute (e.g., /Users/<USER>/project/file.js)\n  - Relative (e.g., src/components/Button.tsx, starting WITHOUT '/')\n  \n  The path must be complete and clear. When providing edits instead of the complete file, include clear comments explaining the changes.\n\n  Always respond in the same language that the user writes in. If the user messages in English, respond in English; if in Chinese, respond in Chinese; and so forth. Language matching is crucial for effective communication.\n\nEres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones.\nCurrent Date is: 2025-06-04T19:32:01.548Z\n\n<user>\n```c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts\n\ndeclare global {\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status: \"loading\" | \"indexing\" | \"done\" | \"failed\" | \"paused\" | \"disabled\";\n    debugInfo?: string;\n  }\n\n  export type PromptTemplate =\n    | string\n    | ((\n        history: ChatMessage[],\n        otherData: Record<string, string>,\n      ) => string | ChatMessage[]);\n\n  export interface ILLM extends LLMOptions {\n    get providerName(): ModelProvider;\n\n    uniqueId: string;\n    model: string;\n\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n\n    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;\n\n    streamComplete(\n      prompt: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamFim(\n      prefix: string,\n      suffix: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamChat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n\n    chat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n\n    countTokens(text: string): number;\n\n    supportsImages(): boolean;\n\n    supportsCompletions(): boolean;\n\n    supportsPrefill(): boolean;\n\n    supportsFim(): boolean;\n\n    listModels(): Promise<string[]>;\n\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n\n  export interface ContextProviderDescription {\n    title: string;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n  }\n\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: EmbeddingsProvider;\n    reranker: Reranker | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    rootUrl?: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n\n  export interface SiteIndexingConfig {\n    startUrl: string;\n    rootUrl?: string;\n    title: string;\n    maxDepth?: number;\n  }\n\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface PersistedSessionInfo {\n    history: ChatHistory;\n    title: string;\n    workspaceDirectory: string;\n    sessionId: string;\n  }\n\n  export interface SessionInfo {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n\n  export interface RangeInFile {\n    filepath: string;\n    content?: string\n    range: Range;\n  }\n\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\";\n\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n\n  export type MessageContent = string | MessagePart[];\n\n  export interface ChatMessage {\n    role: ChatMessageRole;\n    content: MessageContent;\n  }\n\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface ContextItemWithId {\n    content: string;\n    name: string;\n    description: string;\n    id: ContextItemId;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    editorState?: any;\n    modifiers?: InputModifiers;\n    contextItems: ContextItemWithId[];\n    promptLogs?: PromptLog[];\n  }\n\n  export type ChatHistory = ChatHistoryItem[];\n\n  // LLM\n\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n\n    model?: string;\n  }\n  export interface LLMOptions {\n    model: string;\n\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n\n    useLegacyCompletionsEndpoint?: boolean;\n\n    // Cloudflare options\n    accountId?: string;\n\n    // Azure options\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n\n    // AWS options\n    profile?: string;\n    modelArn?: string;\n\n    // AWS and GCP Options\n    region?: string;\n\n    // GCP Options\n    projectId?: string;\n    capabilities?: ModelCapability;\n\n    // IBM watsonx options\n    watsonxUrl?: string;\n    watsonxCreds?: string;\n    watsonxProjectId?: string;\n    watsonxStopToken?: string;\n    watsonxApiVersion?: string;\n\n    cacheSystemMessage?: boolean;\n  }\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n\n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n\n  // IDE\n\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n\n  export class Thread {\n    name: string;\n    id: number;\n  }\n\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    enableControlServerBeta: boolean;\n    pauseCodebaseIndexOnStart: boolean;\n    enableDebugLogs: boolean;\n  }\n\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(): Promise<string>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    listFolders(): Promise<string[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileStat(filepath: string): Promise<{exists: boolean, type: number}>;\n    readRawFile(filepath: string): Promise<string>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    getContinueDir(): Promise<string>;\n    openFile(path: string): Promise<void>;\n    runCommand(command: string, shouldExecute?: boolean): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    showDiff(\n      filepath: string,\n      newContents: string,\n      stepIndex: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getOpenEditors(): Promise<string[]>;\n    getCurrentFile(): Promise<string | undefined>;\n    getCurrentFileSelection(): Promise<RangeInFile | undefined>;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    errorPopup(message: string): Promise<void>;\n    infoPopup(message: string): Promise<void>;\n\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    getGitHubAuthToken(): Promise<string | undefined>;\n\n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n\n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n    onDidChangeActiveTextSelection(callback: (selection?: Range) => void): void\n    pathSep(): Promise<string>;\n  }\n\n  // Slash Commands\n\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n\n  // Config\n\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n\n  type ContextProviderName =\n    | \"diff\"\n    | \"github\"\n    | \"terminal\"\n    | \"locals\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\";\n\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"llama3\";\n\n  type ModelProvider =\n    | \"openai\"\n    | \"free-trial\"\n    | \"anthropic\"\n    | \"cohere\"\n    | \"together\"\n    | \"ollama\"\n    | \"huggingface-tgi\"\n    | \"huggingface-inference-api\"\n    | \"kindo\"\n    | \"llama.cpp\"\n    | \"replicate\"\n    | \"text-gen-webui\"\n    | \"lmstudio\"\n    | \"llamafile\"\n    | \"gemini\"\n    | \"mistral\"\n    | \"bedrock\"\n    | \"bedrockimport\"\n    | \"sagemaker\"\n    | \"deepinfra\"\n    | \"flowise\"\n    | \"groq\"\n    | \"continue-proxy\"\n    | \"fireworks\"\n    | \"custom\"\n    | \"cloudflare\"\n    | \"deepseek\"\n    | \"azure\"\n    | \"openai-aiohttp\"\n    | \"msty\"\n    | \"watsonx\"\n    | \"openrouter\"\n    | \"nvidia\";\n\n  export type ModelName =\n    | \"AUTODETECT\"\n    // OpenAI\n    | \"gpt-3.5-turbo\"\n    | \"gpt-3.5-turbo-16k\"\n    | \"gpt-4\"\n    | \"gpt-3.5-turbo-0613\"\n    | \"gpt-4-32k\"\n    | \"gpt-4o\"\n    | \"gpt-4o-mini\"\n    | \"gpt-4-turbo\"\n    | \"gpt-4-turbo-preview\"\n    | \"gpt-4-vision-preview\"\n    // Mistral\n    | \"codestral-latest\"\n    | \"open-mistral-7b\"\n    | \"open-mixtral-8x7b\"\n    | \"open-mixtral-8x22b\"\n    | \"mistral-small-latest\"\n    | \"mistral-large-latest\"\n    | \"mistral-7b\"\n    | \"mistral-8x7b\"\n    // Llama 2\n    | \"llama2-7b\"\n    | \"llama2-13b\"\n    | \"llama2-70b\"\n    | \"codellama-7b\"\n    | \"codellama-13b\"\n    | \"codellama-34b\"\n    | \"codellama-70b\"\n    // Llama 3\n    | \"llama3-8b\"\n    | \"llama3-70b\"\n    // Other Open-source\n    | \"phi2\"\n    | \"phind-codellama-34b\"\n    | \"wizardcoder-7b\"\n    | \"wizardcoder-13b\"\n    | \"wizardcoder-34b\"\n    | \"zephyr-7b\"\n    | \"codeup-13b\"\n    | \"deepseek-7b\"\n    | \"deepseek-33b\"\n    | \"neural-chat-7b\"\n    // Anthropic\n    | \"claude-3-5-sonnet-20240620\"\n    | \"claude-3-opus-20240229\"\n    | \"claude-3-sonnet-20240229\"\n    | \"claude-3-haiku-20240307\"\n    | \"claude-2.1\"\n    | \"claude-2\"\n    // Cohere\n    | \"command-r\"\n    | \"command-r-plus\"\n    // Gemini\n    | \"gemini-pro\"\n    | \"gemini-1.5-pro-latest\"\n    | \"gemini-1.5-pro\"\n    | \"gemini-1.5-flash-latest\"\n    | \"gemini-1.5-flash\"\n    // Mistral\n    | \"mistral-tiny\"\n    | \"mistral-small\"\n    | \"mistral-medium\"\n    // Tab autocomplete\n    | \"deepseek-1b\"\n    | \"starcoder-1b\"\n    | \"starcoder-3b\"\n    | \"starcoder2-3b\"\n    | \"stable-code-3b\";\n\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    keepAlive?: number;\n    raw?: boolean;\n    stream?: boolean;\n  }\n\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n\n  export interface ModelDescription {\n    title: string;\n    provider: ModelProvider;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n  }\n\n  export type EmbeddingsProviderName =\n    | \"huggingface-tei\"\n    | \"transformers.js\"\n    | \"ollama\"\n    | \"openai\"\n    | \"cohere\"\n    | \"free-trial\"\n    | \"gemini\"\n    | \"continue-proxy\"\n    | \"deepinfra\"\n    | \"voyage\";\n\n  export interface EmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    engine?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n  }\n\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: EmbeddingsProviderName;\n  }\n\n  export interface EmbeddingsProvider {\n    id: string;\n    providerName: EmbeddingsProviderName;\n    maxChunkSize: number;\n    embed(chunks: string[]): Promise<number[][]>;\n  }\n\n  export type RerankerName =\n    | \"cohere\"\n    | \"voyage\"\n    | \"llm\"\n    | \"free-trial\"\n    | \"huggingface-tei\"\n    | \"continue-proxy\";\n\n  export interface RerankerDescription {\n    name: RerankerName;\n    params?: { [key: string]: any };\n  }\n\n  export interface Reranker {\n    name: string;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  }\n\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    useCopyBuffer: boolean;\n    useFileSuffix: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    maxSnippetPercentage: number;\n    recentlyEditedSimilarityThreshold: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useOtherFiles: boolean;\n    useRecentlyEdited: boolean;\n    recentLinePrefixMatchMinLength: number;\n    disableInFiles?: string[];\n    useImports?: boolean;\n  }\n\n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n  }\n\n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n\n  interface ModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n  }\n\n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n\n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n\n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n\n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n\n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n\n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n\n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  }\n\n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n\n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n\n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n\n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | EmbeddingsProvider;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | Reranker;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n\n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: EmbeddingsProvider;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: Reranker;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n  }\n\n}\n\nexport {};\n\n```\n\nThis is the currently open file:\n\n```.monica-code/types/core/index.d.ts\n\n```\n\n/read-large-file index.d.ts \n\n<assistant>\n# TypeScript Declaration File Analysis: Core Type System\n\nI've analyzed the provided TypeScript declaration file (`index.d.ts`), which defines a comprehensive type system for what appears to be an AI coding assistant named \"Continue\" (based on numerous references throughout the file).\n\n## File Overview\n\nThis is a global declaration file that defines the core type system for the application, making these types available throughout the codebase without explicit imports. The file establishes a robust foundation for an AI-powered coding assistant with extensive IDE integration.\n\n## Key Components\n\n### 1. LLM (Large Language Model) System\n- `ILLM` interface defines core LLM functionality including:\n  - Methods for text completion, chat, token counting\n  - Streaming capabilities\n  - Support for numerous model providers (OpenAI, Anthropic, Cohere, etc.)\n- Comprehensive configuration options via `LLMOptions`, `CompletionOptions`, etc.\n- Support for customizing model behavior through templates and system messages\n\n### 2. Context Provider System\n- `IContextProvider` and related interfaces create a pluggable system for providing context\n- Context items can be retrieved from various sources (codebase, terminal, problems, etc.)\n- Custom context providers can be implemented\n\n### 3. IDE Integration\n- Extensive `IDE` interface for interacting with editors like VSCode and JetBrains\n- File operations (read, write, diff, stat)\n- Editor state (selections, open files, problems)\n- Command execution and UI interactions\n\n### 4. Chat System\n- `ChatMessage`, `ChatHistory` interfaces for conversation management\n- Support for multi-modal content (text + images)\n- Different message roles (user, assistant, system)\n\n### 5. Configuration System\n- Multiple configuration interfaces (`ContinueConfig`, `SerializedContinueConfig`)\n- Configuration for models, providers, UI options, etc.\n- Merge behavior for combining different configuration sources\n\n### 6. Advanced Features\n- Slash commands system for custom commands\n- Embeddings and reranking for semantic search\n- Tab autocompletion system\n- Code chunking for analysis\n- Experimental features (context menu prompts, quick actions)\n\n## Architecture Insights\n\nThe type system reveals a well-structured application with clear separation of concerns:\n- Model interaction is abstracted through interfaces\n- IDE integration is comprehensive but modular\n- Plugin-like systems for context providers and slash commands\n- Configuration is flexible and extensible\n\nThis appears to be a sophisticated AI coding assistant tool that can integrate with development environments and provide contextual assistance using various LLM providers.\n\n<user>\n```.monica-code/AGENT-ANALYSIS.md\n\n```\n\n```.monica-code/README.md\n\n```\n\n```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```\n\nThis is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```\n\nlee estoa 4 archivos a la vez\n\n<assistant>\nVoy a leer los cuatro archivos solicitados:\n\n<tool>\nCould not find file .monica-code/AGENT-ANALYSIS.md\n\n<assistant>\nVeo que el primer archivo `.monica-code/AGENT-ANALYSIS.md` no se encuentra. Continuaré con los demás archivos de la lista:\n\n<tool>\nCould not find file .monica-code/README.md\n\n<assistant>\nVeo que tampoco se encontró el archivo `.monica-code/README.md`. Continuaré con los otros dos archivos de la lista:\n\n<tool>\nCould not find file .monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n", "completion": "Veo que no se han encontrado tres de los archivos solicitados:\n- `.monica-code/AGENT-ANALYSIS.md`\n- `.monica-code/README.md`\n- `.monica-code/LARGE-FILE-HANDLING-GUIDE.md`\n\nAhora intentaré leer el archivo que mencionas como actualmente abierto:", "completionOptions": {"model": "claude-3-7-sonnet-latest-thinking", "maxTokens": 4096, "tools": [{"type": "function", "function": {"name": "builtin_read_file", "description": "Use this tool whenever you need to view the contents of a file.", "parameters": {"type": "object", "required": ["filepath"], "properties": {"filepath": {"type": "string", "description": "The path of the file to read, relative to the root of the workspace."}}}}}, {"type": "function", "function": {"name": "builtin_create_new_file", "description": "Create a new file", "parameters": {"type": "object", "required": ["filepath", "contents"], "properties": {"filepath": {"type": "string", "description": "The path where the new file should be created"}, "contents": {"type": "string", "description": "The contents to write to the new file"}}}}}, {"type": "function", "function": {"name": "builtin_run_terminal_command", "description": "Run a terminal command in the current directory. The shell is not stateful and will not remember any previous commands.", "parameters": {"type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The command to run. This will be passed directly into the shell."}}}}}, {"type": "function", "function": {"name": "builtin_view_subdirectory", "description": "View the contents of a subdirectory", "parameters": {"type": "object", "required": ["directory_path"], "properties": {"directory_path": {"type": "string", "description": "The path of the subdirectory to view, relative to the root of the workspace"}}}}}, {"type": "function", "function": {"name": "builtin_view_repo_map", "description": "View the repository map", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_exact_search", "description": "Perform an exact file contents search over the repository using grep.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query to use. Must be a valid ripgrep regex expression, escaped where needed"}}}}}, {"type": "function", "function": {"name": "builtin_search_web", "description": "Performs a web search, returning top results. This tool should only be called for questions that require external knowledge. Common programming questions do not require web search.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The natural language search query"}}}}}, {"type": "function", "function": {"name": "builtin_view_diff", "description": "View the current diff of working changes", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_read_currently_open_file", "description": "Read the currently open file in the IDE. If the user seems to be referring to a file that you can't see, this is probably it.", "parameters": {"type": "object", "properties": {}}}}]}}], "modelName": "Claude 3.7 Sonnet Thinking", "reasoningContent": "Veo que tampoco se encontró el archivo `.monica-code/LARGE-FILE-HANDLING-GUIDE.md`. Queda por revisar el archivo que está actualmente abierto, según la información proporcionada por el usuario:\n\nIntentaré leer el archivo `.monica-code/AUTONOMOUS-AGENT-GUIDE.md` que según la información del usuario está actualmente abierto.", "id": "f7b3d91e-eafd-44f8-ae63-6ec5e83c2057", "content": "Veo que no se han encontrado tres de los archivos solicitados:\n- `.monica-code/AGENT-ANALYSIS.md`\n- `.monica-code/README.md`\n- `.monica-code/LARGE-FILE-HANDLING-GUIDE.md`\n\nAhora intentaré leer el archivo que mencionas como actualmente abierto:", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065521526, "parentId": "645a354b-2eaf-49a7-b862-ca436c6c45b7", "role": "assistant", "toolCalls": [{"id": "toolu_012gFhoQ7PW29wjXBCDZeqzk", "type": "function", "function": {"name": "builtin_read_file", "arguments": "{\"filepath\": \".monica-code/AUTONOMOUS-AGENT-GUIDE.md\"}"}}]}, {"id": "74c7b989-3568-4bc3-a17b-7a912f1bfcaa", "content": "Could not find file .monica-code/AUTONOMOUS-AGENT-GUIDE.md", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065526690, "parentId": "f7b3d91e-eafd-44f8-ae63-6ec5e83c2057", "role": "tool", "toolCallId": "toolu_012gFhoQ7PW29wjXBCDZeqzk", "toolName": "builtin_read_file", "parsedArgs": {"filepath": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, "toolInput": "{\"filepath\": \".monica-code/AUTONOMOUS-AGENT-GUIDE.md\"}", "status": "error", "toolOutput": [{"id": {"providerTitle": "1", "itemId": "1"}, "name": "Tool Failed", "description": "Tool Failed", "content": "The tool call was failed. Please try something else or request further instructions.", "hidden": true}], "toolError": "Could not find file .monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, {"codeblockIds": [], "contextResults": [{"name": "AGENT-ANALYSIS.md", "description": ".monica-code/AGENT-ANALYSIS.md", "content": "```.monica-code/AGENT-ANALYSIS.md\n\n```", "uri": {"type": "file", "value": ".monica-code/AGENT-ANALYSIS.md"}, "id": {"providerTitle": "file", "itemId": "a46f9108-94d3-4477-83bc-9a6f74e25cee"}}, {"name": "README.md", "description": ".monica-code/README.md", "content": "```.monica-code/README.md\n\n```", "uri": {"type": "file", "value": ".monica-code/README.md"}, "id": {"providerTitle": "file", "itemId": "8538904c-16f3-4f7b-8b38-29aea47fd36d"}}, {"name": "LARGE-FILE-HANDLING-GUIDE.md", "description": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md", "content": "```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```", "uri": {"type": "file", "value": ".monica-code/LARGE-FILE-HANDLING-GUIDE.md"}, "id": {"providerTitle": "file", "itemId": "764070e4-eef9-4b1b-99dd-1833118d1291"}}, {"description": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md", "content": "This is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```", "name": "AUTONOMOUS-AGENT-GUIDE.md", "uri": {"type": "file", "value": ".monica-code/AUTONOMOUS-AGENT-GUIDE.md"}, "id": {"providerTitle": "currentFile", "itemId": "37f06d34-3898-4bae-a012-160d3202c6e2"}}], "isComplete": true, "promptLogs": [{"modelTitle": "Claude 3.7 Sonnet Thinking", "prompt": "<system>\nWhen you need to edit or create new files, use the format ```lang:path at the start of a code block. For example:\n\n  - To edit an existing file: ```ts:path/to/edit.ts\n  - To create a new file: ```py:path/to/new/file.py\n  \n  The path can be either:\n  - Absolute (e.g., /Users/<USER>/project/file.js)\n  - Relative (e.g., src/components/Button.tsx, starting WITHOUT '/')\n  \n  The path must be complete and clear. When providing edits instead of the complete file, include clear comments explaining the changes.\n\n  Always respond in the same language that the user writes in. If the user messages in English, respond in English; if in Chinese, respond in Chinese; and so forth. Language matching is crucial for effective communication.\n\nEres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones.\nCurrent Date is: 2025-06-04T19:32:07.273Z\n\n<user>\n```c:\\Users\\<USER>\\.monica-code\\types\\core\\index.d.ts\n\ndeclare global {\n  export interface ChunkWithoutID {\n    content: string;\n    startLine: number;\n    endLine: number;\n    signature?: string;\n    otherMetadata?: { [key: string]: any };\n  }\n\n  export interface Chunk extends ChunkWithoutID {\n    digest: string;\n    filepath: string;\n    index: number; // Index of the chunk in the document at filepath\n  }\n\n  export interface IndexingProgressUpdate {\n    progress: number;\n    desc: string;\n    shouldClearIndexes?: boolean;\n    status: \"loading\" | \"indexing\" | \"done\" | \"failed\" | \"paused\" | \"disabled\";\n    debugInfo?: string;\n  }\n\n  export type PromptTemplate =\n    | string\n    | ((\n        history: ChatMessage[],\n        otherData: Record<string, string>,\n      ) => string | ChatMessage[]);\n\n  export interface ILLM extends LLMOptions {\n    get providerName(): ModelProvider;\n\n    uniqueId: string;\n    model: string;\n\n    title?: string;\n    systemMessage?: string;\n    contextLength: number;\n    maxStopWords?: number;\n    completionOptions: CompletionOptions;\n    requestOptions?: RequestOptions;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    apiBase?: string;\n\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n    region?: string;\n    projectId?: string;\n\n    complete(prompt: string, options?: LLMFullCompletionOptions): Promise<string>;\n\n    streamComplete(\n      prompt: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamFim(\n      prefix: string,\n      suffix: string,\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<string, PromptLog>;\n\n    streamChat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): AsyncGenerator<ChatMessage, PromptLog>;\n\n    chat(\n      messages: ChatMessage[],\n      options?: LLMFullCompletionOptions,\n    ): Promise<ChatMessage>;\n\n    countTokens(text: string): number;\n\n    supportsImages(): boolean;\n\n    supportsCompletions(): boolean;\n\n    supportsPrefill(): boolean;\n\n    supportsFim(): boolean;\n\n    listModels(): Promise<string[]>;\n\n    renderPromptTemplate(\n      template: PromptTemplate,\n      history: ChatMessage[],\n      otherData: Record<string, string>,\n      canPutWordsInModelsMouth?: boolean,\n    ): string | ChatMessage[];\n  }\n\n  export type ContextProviderType = \"normal\" | \"query\" | \"submenu\";\n\n  export interface ContextProviderDescription {\n    title: string;\n    displayTitle: string;\n    description: string;\n    renderInlineAs?: string;\n    type: ContextProviderType;\n  }\n\n  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;\n\n  export interface ContextProviderExtras {\n    config: ContinueConfig;\n    fullInput: string;\n    embeddingsProvider: EmbeddingsProvider;\n    reranker: Reranker | undefined;\n    llm: ILLM;\n    ide: IDE;\n    selectedCode: RangeInFile[];\n    fetch: FetchFunction;\n  }\n\n  export interface LoadSubmenuItemsArgs {\n    config: ContinueConfig;\n    ide: IDE;\n    fetch: FetchFunction;\n  }\n\n  export interface CustomContextProvider {\n    title: string;\n    displayTitle?: string;\n    description?: string;\n    renderInlineAs?: string;\n    type?: ContextProviderType;\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n    loadSubmenuItems?: (\n      args: LoadSubmenuItemsArgs,\n    ) => Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface ContextSubmenuItem {\n    id: string;\n    title: string;\n    description: string;\n    icon?: string;\n    metadata?: any;\n  }\n\n  export interface SiteIndexingConfig {\n    title: string;\n    startUrl: string;\n    rootUrl?: string;\n    maxDepth?: number;\n    faviconUrl?: string;\n  }\n\n  export interface SiteIndexingConfig {\n    startUrl: string;\n    rootUrl?: string;\n    title: string;\n    maxDepth?: number;\n  }\n\n  export interface IContextProvider {\n    get description(): ContextProviderDescription;\n\n    getContextItems(\n      query: string,\n      extras: ContextProviderExtras,\n    ): Promise<ContextItem[]>;\n\n    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;\n  }\n\n  export interface PersistedSessionInfo {\n    history: ChatHistory;\n    title: string;\n    workspaceDirectory: string;\n    sessionId: string;\n  }\n\n  export interface SessionInfo {\n    sessionId: string;\n    title: string;\n    dateCreated: string;\n    workspaceDirectory: string;\n  }\n\n  export interface RangeInFile {\n    filepath: string;\n    content?: string\n    range: Range;\n  }\n\n  export interface Location {\n    filepath: string;\n    position: Position;\n  }\n\n  export interface FileWithContents {\n    filepath: string;\n    contents: string;\n  }\n\n  export interface Range {\n    start: Position;\n    end: Position;\n  }\n  export interface Position {\n    line: number;\n    character: number;\n  }\n  export interface FileEdit {\n    filepath: string;\n    range: Range;\n    replacement: string;\n  }\n\n  export interface ContinueError {\n    title: string;\n    message: string;\n  }\n\n  export interface CompletionOptions extends BaseCompletionOptions {\n    model: string;\n  }\n\n  export type ChatMessageRole = \"user\" | \"assistant\" | \"system\";\n\n  export interface MessagePart {\n    type: \"text\" | \"imageUrl\";\n    text?: string;\n    imageUrl?: { url: string };\n  }\n\n  export type MessageContent = string | MessagePart[];\n\n  export interface ChatMessage {\n    role: ChatMessageRole;\n    content: MessageContent;\n  }\n\n  export interface ContextItemId {\n    providerTitle: string;\n    itemId: string;\n  }\n\n  export interface ContextItem {\n    content: string;\n    name: string;\n    description: string;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface ContextItemWithId {\n    content: string;\n    name: string;\n    description: string;\n    id: ContextItemId;\n    editing?: boolean;\n    editable?: boolean;\n    icon?: string;\n  }\n\n  export interface InputModifiers {\n    useCodebase: boolean;\n    noContext: boolean;\n  }\n\n  export interface PromptLog {\n    modelTitle: string;\n    completionOptions: CompletionOptions;\n    prompt: string;\n    completion: string;\n  }\n\n  export interface ChatHistoryItem {\n    message: ChatMessage;\n    editorState?: any;\n    modifiers?: InputModifiers;\n    contextItems: ContextItemWithId[];\n    promptLogs?: PromptLog[];\n  }\n\n  export type ChatHistory = ChatHistoryItem[];\n\n  // LLM\n\n  export interface LLMFullCompletionOptions extends BaseCompletionOptions {\n    log?: boolean;\n\n    model?: string;\n  }\n  export interface LLMOptions {\n    model: string;\n\n    title?: string;\n    uniqueId?: string;\n    systemMessage?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    completionOptions?: CompletionOptions;\n    requestOptions?: RequestOptions;\n    template?: TemplateType;\n    promptTemplates?: Record<string, PromptTemplate>;\n    templateMessages?: (messages: ChatMessage[]) => string;\n    writeLog?: (str: string) => Promise<void>;\n    llmRequestHook?: (model: string, prompt: string) => any;\n    apiKey?: string;\n    aiGatewaySlug?: string;\n    apiBase?: string;\n\n    useLegacyCompletionsEndpoint?: boolean;\n\n    // Cloudflare options\n    accountId?: string;\n\n    // Azure options\n    engine?: string;\n    apiVersion?: string;\n    apiType?: string;\n\n    // AWS options\n    profile?: string;\n    modelArn?: string;\n\n    // AWS and GCP Options\n    region?: string;\n\n    // GCP Options\n    projectId?: string;\n    capabilities?: ModelCapability;\n\n    // IBM watsonx options\n    watsonxUrl?: string;\n    watsonxCreds?: string;\n    watsonxProjectId?: string;\n    watsonxStopToken?: string;\n    watsonxApiVersion?: string;\n\n    cacheSystemMessage?: boolean;\n  }\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n    }[Keys];\n\n  export interface CustomLLMWithOptionals {\n    options: LLMOptions;\n    streamCompletion?: (\n      prompt: string,\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    streamChat?: (\n      messages: ChatMessage[],\n      options: CompletionOptions,\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => AsyncGenerator<string>;\n    listModels?: (\n      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,\n    ) => Promise<string[]>;\n  }\n\n  /**\n   * The LLM interface requires you to specify either `streamCompletion` or `streamChat` (or both).\n   */\n  export type CustomLLM = RequireAtLeastOne<\n    CustomLLMWithOptionals,\n    \"streamCompletion\" | \"streamChat\"\n  >;\n\n  // IDE\n\n  export type DiffLineType = \"new\" | \"old\" | \"same\";\n\n  export interface DiffLine {\n    type: DiffLineType;\n    line: string;\n  }\n\n  export class Problem {\n    filepath: string;\n    range: Range;\n    message: string;\n  }\n\n  export class Thread {\n    name: string;\n    id: number;\n  }\n\n  export type IdeType = \"vscode\" | \"jetbrains\";\n  export interface IdeInfo {\n    ideType: IdeType;\n    name: string;\n    version: string;\n    remoteName: string;\n    extensionVersion: string;\n  }\n\n  export interface BranchAndDir {\n    branch: string;\n    directory: string;\n  }\n\n  export interface IndexTag extends BranchAndDir {\n    artifactId: string;\n  }\n\n  export enum FileType {\n    Unkown = 0,\n    File = 1,\n    Directory = 2,\n    SymbolicLink = 64,\n  }\n\n  export interface IdeSettings {\n    remoteConfigServerUrl: string | undefined;\n    remoteConfigSyncPeriod: number;\n    userToken: string;\n    enableControlServerBeta: boolean;\n    pauseCodebaseIndexOnStart: boolean;\n    enableDebugLogs: boolean;\n  }\n\n  export interface IDE {\n    getIdeInfo(): Promise<IdeInfo>;\n    getIdeSettings(): Promise<IdeSettings>;\n    getDiff(): Promise<string>;\n    isTelemetryEnabled(): Promise<boolean>;\n    getUniqueId(): Promise<string>;\n    getTerminalContents(): Promise<string>;\n    getDebugLocals(threadIndex: number): Promise<string>;\n    getTopLevelCallStackSources(\n      threadIndex: number,\n      stackDepth: number,\n    ): Promise<string[]>;\n    getAvailableThreads(): Promise<Thread[]>;\n    listFolders(): Promise<string[]>;\n    getWorkspaceDirs(): Promise<string[]>;\n    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;\n    fileStat(filepath: string): Promise<{exists: boolean, type: number}>;\n    readRawFile(filepath: string): Promise<string>;\n    writeFile(path: string, contents: string): Promise<void>;\n    showVirtualFile(title: string, contents: string): Promise<void>;\n    getContinueDir(): Promise<string>;\n    openFile(path: string): Promise<void>;\n    runCommand(command: string, shouldExecute?: boolean): Promise<void>;\n    saveFile(filepath: string): Promise<void>;\n    readFile(filepath: string): Promise<string>;\n    readRangeInFile(filepath: string, range: Range): Promise<string>;\n    showLines(\n      filepath: string,\n      startLine: number,\n      endLine: number,\n    ): Promise<void>;\n    showDiff(\n      filepath: string,\n      newContents: string,\n      stepIndex: number,\n    ): Promise<void>;\n    getOpenFiles(): Promise<string[]>;\n    getOpenEditors(): Promise<string[]>;\n    getCurrentFile(): Promise<string | undefined>;\n    getCurrentFileSelection(): Promise<RangeInFile | undefined>;\n    getPinnedFiles(): Promise<string[]>;\n    getSearchResults(query: string): Promise<string>;\n    getProblems(filepath?: string | undefined): Promise<Problem[]>;\n    getBranch(dir: string): Promise<string>;\n    getTags(artifactId: string): Promise<IndexTag[]>;\n    getRepoName(dir: string): Promise<string | undefined>;\n    errorPopup(message: string): Promise<void>;\n    infoPopup(message: string): Promise<void>;\n\n    getGitRootPath(dir: string): Promise<string | undefined>;\n    listDir(dir: string): Promise<[string, FileType][]>;\n    getLastModified(files: string[]): Promise<{ [path: string]: number }>;\n    getGitHubAuthToken(): Promise<string | undefined>;\n\n    // LSP\n    gotoDefinition(location: Location): Promise<RangeInFile[]>;\n\n    // Callbacks\n    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;\n    onDidChangeActiveTextSelection(callback: (selection?: Range) => void): void\n    pathSep(): Promise<string>;\n  }\n\n  // Slash Commands\n\n  export interface ContinueSDK {\n    ide: IDE;\n    llm: ILLM;\n    addContextItem: (item: ContextItemWithId) => void;\n    history: ChatMessage[];\n    input: string;\n    params?: { [key: string]: any } | undefined;\n    contextItems: ContextItemWithId[];\n    selectedCode: RangeInFile[];\n    config: ContinueConfig;\n    fetch: FetchFunction;\n  }\n\n  export interface SlashCommand {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;\n  }\n\n  // Config\n\n  type StepName =\n    | \"AnswerQuestionChroma\"\n    | \"GenerateShellCommandStep\"\n    | \"EditHighlightedCodeStep\"\n    | \"ShareSessionStep\"\n    | \"CommentCodeStep\"\n    | \"ClearHistoryStep\"\n    | \"StackOverflowStep\"\n    | \"OpenConfigStep\"\n    | \"GenerateShellCommandStep\"\n    | \"DraftIssueStep\";\n\n  type ContextProviderName =\n    | \"diff\"\n    | \"github\"\n    | \"terminal\"\n    | \"locals\"\n    | \"open\"\n    | \"google\"\n    | \"search\"\n    | \"tree\"\n    | \"http\"\n    | \"codebase\"\n    | \"problems\"\n    | \"folder\"\n    | \"jira\"\n    | \"postgres\"\n    | \"database\"\n    | \"code\"\n    | \"docs\"\n    | \"gitlab-mr\"\n    | \"os\"\n    | \"currentFile\";\n\n  type TemplateType =\n    | \"llama2\"\n    | \"alpaca\"\n    | \"zephyr\"\n    | \"phi2\"\n    | \"phind\"\n    | \"anthropic\"\n    | \"chatml\"\n    | \"none\"\n    | \"openchat\"\n    | \"deepseek\"\n    | \"xwin-coder\"\n    | \"neural-chat\"\n    | \"codellama-70b\"\n    | \"llava\"\n    | \"gemma\"\n    | \"llama3\";\n\n  type ModelProvider =\n    | \"openai\"\n    | \"free-trial\"\n    | \"anthropic\"\n    | \"cohere\"\n    | \"together\"\n    | \"ollama\"\n    | \"huggingface-tgi\"\n    | \"huggingface-inference-api\"\n    | \"kindo\"\n    | \"llama.cpp\"\n    | \"replicate\"\n    | \"text-gen-webui\"\n    | \"lmstudio\"\n    | \"llamafile\"\n    | \"gemini\"\n    | \"mistral\"\n    | \"bedrock\"\n    | \"bedrockimport\"\n    | \"sagemaker\"\n    | \"deepinfra\"\n    | \"flowise\"\n    | \"groq\"\n    | \"continue-proxy\"\n    | \"fireworks\"\n    | \"custom\"\n    | \"cloudflare\"\n    | \"deepseek\"\n    | \"azure\"\n    | \"openai-aiohttp\"\n    | \"msty\"\n    | \"watsonx\"\n    | \"openrouter\"\n    | \"nvidia\";\n\n  export type ModelName =\n    | \"AUTODETECT\"\n    // OpenAI\n    | \"gpt-3.5-turbo\"\n    | \"gpt-3.5-turbo-16k\"\n    | \"gpt-4\"\n    | \"gpt-3.5-turbo-0613\"\n    | \"gpt-4-32k\"\n    | \"gpt-4o\"\n    | \"gpt-4o-mini\"\n    | \"gpt-4-turbo\"\n    | \"gpt-4-turbo-preview\"\n    | \"gpt-4-vision-preview\"\n    // Mistral\n    | \"codestral-latest\"\n    | \"open-mistral-7b\"\n    | \"open-mixtral-8x7b\"\n    | \"open-mixtral-8x22b\"\n    | \"mistral-small-latest\"\n    | \"mistral-large-latest\"\n    | \"mistral-7b\"\n    | \"mistral-8x7b\"\n    // Llama 2\n    | \"llama2-7b\"\n    | \"llama2-13b\"\n    | \"llama2-70b\"\n    | \"codellama-7b\"\n    | \"codellama-13b\"\n    | \"codellama-34b\"\n    | \"codellama-70b\"\n    // Llama 3\n    | \"llama3-8b\"\n    | \"llama3-70b\"\n    // Other Open-source\n    | \"phi2\"\n    | \"phind-codellama-34b\"\n    | \"wizardcoder-7b\"\n    | \"wizardcoder-13b\"\n    | \"wizardcoder-34b\"\n    | \"zephyr-7b\"\n    | \"codeup-13b\"\n    | \"deepseek-7b\"\n    | \"deepseek-33b\"\n    | \"neural-chat-7b\"\n    // Anthropic\n    | \"claude-3-5-sonnet-20240620\"\n    | \"claude-3-opus-20240229\"\n    | \"claude-3-sonnet-20240229\"\n    | \"claude-3-haiku-20240307\"\n    | \"claude-2.1\"\n    | \"claude-2\"\n    // Cohere\n    | \"command-r\"\n    | \"command-r-plus\"\n    // Gemini\n    | \"gemini-pro\"\n    | \"gemini-1.5-pro-latest\"\n    | \"gemini-1.5-pro\"\n    | \"gemini-1.5-flash-latest\"\n    | \"gemini-1.5-flash\"\n    // Mistral\n    | \"mistral-tiny\"\n    | \"mistral-small\"\n    | \"mistral-medium\"\n    // Tab autocomplete\n    | \"deepseek-1b\"\n    | \"starcoder-1b\"\n    | \"starcoder-3b\"\n    | \"starcoder2-3b\"\n    | \"stable-code-3b\";\n\n  export interface RequestOptions {\n    timeout?: number;\n    verifySsl?: boolean;\n    caBundlePath?: string | string[];\n    proxy?: string;\n    headers?: { [key: string]: string };\n    extraBodyProperties?: { [key: string]: any };\n    noProxy?: string[];\n    clientCertificate?: ClientCertificateOptions;\n  }\n\n  export interface ClientCertificateOptions {\n    cert: string;\n    key: string;\n    passphrase?: string;\n  }\n\n  export interface StepWithParams {\n    name: StepName;\n    params: { [key: string]: any };\n  }\n\n  export interface ContextProviderWithParams {\n    name: ContextProviderName;\n    params: { [key: string]: any };\n  }\n\n  export interface SlashCommandDescription {\n    name: string;\n    description: string;\n    params?: { [key: string]: any };\n  }\n\n  export interface CustomCommand {\n    name: string;\n    prompt: string;\n    description: string;\n  }\n\n  interface BaseCompletionOptions {\n    temperature?: number;\n    topP?: number;\n    topK?: number;\n    minP?: number;\n    presencePenalty?: number;\n    frequencyPenalty?: number;\n    mirostat?: number;\n    stop?: string[];\n    maxTokens?: number;\n    numThreads?: number;\n    keepAlive?: number;\n    raw?: boolean;\n    stream?: boolean;\n  }\n\n  export interface ModelCapability {\n    uploadImage?: boolean;\n  }\n\n  export interface ModelDescription {\n    title: string;\n    provider: ModelProvider;\n    model: string;\n    apiKey?: string;\n    apiBase?: string;\n    contextLength?: number;\n    maxStopWords?: number;\n    template?: TemplateType;\n    completionOptions?: BaseCompletionOptions;\n    systemMessage?: string;\n    requestOptions?: RequestOptions;\n    promptTemplates?: { [key: string]: string };\n    capabilities?: ModelCapability;\n  }\n\n  export type EmbeddingsProviderName =\n    | \"huggingface-tei\"\n    | \"transformers.js\"\n    | \"ollama\"\n    | \"openai\"\n    | \"cohere\"\n    | \"free-trial\"\n    | \"gemini\"\n    | \"continue-proxy\"\n    | \"deepinfra\"\n    | \"voyage\";\n\n  export interface EmbedOptions {\n    apiBase?: string;\n    apiKey?: string;\n    model?: string;\n    engine?: string;\n    apiType?: string;\n    apiVersion?: string;\n    requestOptions?: RequestOptions;\n    maxChunkSize?: number;\n  }\n\n  export interface EmbeddingsProviderDescription extends EmbedOptions {\n    provider: EmbeddingsProviderName;\n  }\n\n  export interface EmbeddingsProvider {\n    id: string;\n    providerName: EmbeddingsProviderName;\n    maxChunkSize: number;\n    embed(chunks: string[]): Promise<number[][]>;\n  }\n\n  export type RerankerName =\n    | \"cohere\"\n    | \"voyage\"\n    | \"llm\"\n    | \"free-trial\"\n    | \"huggingface-tei\"\n    | \"continue-proxy\";\n\n  export interface RerankerDescription {\n    name: RerankerName;\n    params?: { [key: string]: any };\n  }\n\n  export interface Reranker {\n    name: string;\n    rerank(query: string, chunks: Chunk[]): Promise<number[]>;\n  }\n\n  export interface TabAutocompleteOptions {\n    disable: boolean;\n    useCopyBuffer: boolean;\n    useFileSuffix: boolean;\n    maxPromptTokens: number;\n    debounceDelay: number;\n    maxSuffixPercentage: number;\n    prefixPercentage: number;\n    template?: string;\n    multilineCompletions: \"always\" | \"never\" | \"auto\";\n    slidingWindowPrefixPercentage: number;\n    slidingWindowSize: number;\n    maxSnippetPercentage: number;\n    recentlyEditedSimilarityThreshold: number;\n    useCache: boolean;\n    onlyMyCode: boolean;\n    useOtherFiles: boolean;\n    useRecentlyEdited: boolean;\n    recentLinePrefixMatchMinLength: number;\n    disableInFiles?: string[];\n    useImports?: boolean;\n  }\n\n  export interface ContinueUIConfig {\n    codeBlockToolbarPosition?: \"top\" | \"bottom\";\n    fontSize?: number;\n    displayRawMarkdown?: boolean;\n  }\n\n  interface ContextMenuConfig {\n    comment?: string;\n    docstring?: string;\n    fix?: string;\n    optimize?: string;\n    fixGrammar?: string;\n  }\n\n  interface ModelRoles {\n    inlineEdit?: string;\n    applyCodeBlock?: string;\n  }\n\n  /**\n   * Represents the configuration for a quick action in the Code Lens.\n   * Quick actions are custom commands that can be added to function and class declarations.\n   */\n  interface QuickActionConfig {\n    /**\n     * The title of the quick action that will display in the Code Lens.\n     */\n    title: string;\n\n    /**\n     * The prompt that will be sent to the model when the quick action is invoked,\n     * with the function or class body concatenated.\n     */\n    prompt: string;\n\n    /**\n     * If `true`, the result of the quick action will be sent to the chat panel.\n     * If `false`, the streamed result will be inserted into the document.\n     *\n     * Defaults to `false`.\n     */\n    sendToChat: boolean;\n  }\n\n  export type DefaultContextProvider = ContextProviderWithParams & {\n    query?: string;\n  };\n\n  interface ExperimentalConfig {\n    contextMenuPrompts?: ContextMenuConfig;\n    modelRoles?: ModelRoles;\n    defaultContext?: DefaultContextProvider[];\n    promptPath?: string;\n\n    /**\n     * Quick actions are a way to add custom commands to the Code Lens of\n     * function and class declarations.\n     */\n    quickActions?: QuickActionConfig[];\n\n    /**\n     * Automatically read LLM chat responses aloud using system TTS models\n     */\n    readResponseTTS?: boolean;\n  }\n\n  interface AnalyticsConfig {\n    type: string;\n    url?: string;\n    clientKey?: string;\n  }\n\n  // config.json\n  export interface SerializedContinueConfig {\n    env?: string[];\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    customCommands?: CustomCommand[];\n    contextProviders?: ContextProviderWithParams[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: EmbeddingsProviderDescription;\n    tabAutocompleteModel?: ModelDescription | ModelDescription[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export type ConfigMergeType = \"merge\" | \"overwrite\";\n\n  export type ContinueRcJson = Partial<SerializedContinueConfig> & {\n    mergeBehavior: ConfigMergeType;\n  };\n\n  // config.ts - give users simplified interfaces\n  export interface Config {\n    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */\n    allowAnonymousTelemetry?: boolean;\n    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.\n     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either `streamCompletion` or `streamChat` (or both).\n     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.\n     */\n    models: (CustomLLM | ModelDescription)[];\n    /** A system message to be followed by all of your models */\n    systemMessage?: string;\n    /** The default completion options for all models */\n    completionOptions?: BaseCompletionOptions;\n    /** Request options that will be applied to all models and context providers */\n    requestOptions?: RequestOptions;\n    /** The list of slash commands that will be available in the sidebar */\n    slashCommands?: SlashCommand[];\n    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.\n     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call `getContextItems(query)`.\n     */\n    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];\n    /** If set to true, Continue will not index your codebase for retrieval */\n    disableIndexing?: boolean;\n    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */\n    disableSessionTitles?: boolean;\n    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */\n    userToken?: string;\n    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */\n    embeddingsProvider?: EmbeddingsProviderDescription | EmbeddingsProvider;\n    /** The model that Continue will use for tab autocompletions. */\n    tabAutocompleteModel?:\n      | CustomLLM\n      | ModelDescription\n      | (CustomLLM | ModelDescription)[];\n    /** Options for tab autocomplete */\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    /** UI styles customization */\n    ui?: ContinueUIConfig;\n    /** Options for the reranker */\n    reranker?: RerankerDescription | Reranker;\n    /** Experimental configuration */\n    experimental?: ExperimentalConfig;\n    /** Analytics configuration */\n    analytics?: AnalyticsConfig;\n  }\n\n  // in the actual Continue source code\n  export interface ContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ILLM[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommand[];\n    contextProviders?: IContextProvider[];\n    disableSessionTitles?: boolean;\n    disableIndexing?: boolean;\n    userToken?: string;\n    embeddingsProvider: EmbeddingsProvider;\n    tabAutocompleteModels?: ILLM[];\n    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;\n    ui?: ContinueUIConfig;\n    reranker?: Reranker;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n    docs?: SiteIndexingConfig[];\n  }\n\n  export interface BrowserSerializedContinueConfig {\n    allowAnonymousTelemetry?: boolean;\n    models: ModelDescription[];\n    systemMessage?: string;\n    completionOptions?: BaseCompletionOptions;\n    requestOptions?: RequestOptions;\n    slashCommands?: SlashCommandDescription[];\n    contextProviders?: ContextProviderDescription[];\n    disableIndexing?: boolean;\n    disableSessionTitles?: boolean;\n    userToken?: string;\n    embeddingsProvider?: string;\n    ui?: ContinueUIConfig;\n    reranker?: RerankerDescription;\n    experimental?: ExperimentalConfig;\n    analytics?: AnalyticsConfig;\n  }\n\n}\n\nexport {};\n\n```\n\nThis is the currently open file:\n\n```.monica-code/types/core/index.d.ts\n\n```\n\n/read-large-file index.d.ts \n\n<assistant>\n# TypeScript Declaration File Analysis: Core Type System\n\nI've analyzed the provided TypeScript declaration file (`index.d.ts`), which defines a comprehensive type system for what appears to be an AI coding assistant named \"Continue\" (based on numerous references throughout the file).\n\n## File Overview\n\nThis is a global declaration file that defines the core type system for the application, making these types available throughout the codebase without explicit imports. The file establishes a robust foundation for an AI-powered coding assistant with extensive IDE integration.\n\n## Key Components\n\n### 1. LLM (Large Language Model) System\n- `ILLM` interface defines core LLM functionality including:\n  - Methods for text completion, chat, token counting\n  - Streaming capabilities\n  - Support for numerous model providers (OpenAI, Anthropic, Cohere, etc.)\n- Comprehensive configuration options via `LLMOptions`, `CompletionOptions`, etc.\n- Support for customizing model behavior through templates and system messages\n\n### 2. Context Provider System\n- `IContextProvider` and related interfaces create a pluggable system for providing context\n- Context items can be retrieved from various sources (codebase, terminal, problems, etc.)\n- Custom context providers can be implemented\n\n### 3. IDE Integration\n- Extensive `IDE` interface for interacting with editors like VSCode and JetBrains\n- File operations (read, write, diff, stat)\n- Editor state (selections, open files, problems)\n- Command execution and UI interactions\n\n### 4. Chat System\n- `ChatMessage`, `ChatHistory` interfaces for conversation management\n- Support for multi-modal content (text + images)\n- Different message roles (user, assistant, system)\n\n### 5. Configuration System\n- Multiple configuration interfaces (`ContinueConfig`, `SerializedContinueConfig`)\n- Configuration for models, providers, UI options, etc.\n- Merge behavior for combining different configuration sources\n\n### 6. Advanced Features\n- Slash commands system for custom commands\n- Embeddings and reranking for semantic search\n- Tab autocompletion system\n- Code chunking for analysis\n- Experimental features (context menu prompts, quick actions)\n\n## Architecture Insights\n\nThe type system reveals a well-structured application with clear separation of concerns:\n- Model interaction is abstracted through interfaces\n- IDE integration is comprehensive but modular\n- Plugin-like systems for context providers and slash commands\n- Configuration is flexible and extensible\n\nThis appears to be a sophisticated AI coding assistant tool that can integrate with development environments and provide contextual assistance using various LLM providers.\n\n<user>\n```.monica-code/AGENT-ANALYSIS.md\n\n```\n\n```.monica-code/README.md\n\n```\n\n```.monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n```\n\nThis is the currently open file:\n\n```.monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n```\n\nlee estoa 4 archivos a la vez\n\n<assistant>\nVoy a leer los cuatro archivos solicitados:\n\n<tool>\nCould not find file .monica-code/AGENT-ANALYSIS.md\n\n<assistant>\nVeo que el primer archivo `.monica-code/AGENT-ANALYSIS.md` no se encuentra. Continuaré con los demás archivos de la lista:\n\n<tool>\nCould not find file .monica-code/README.md\n\n<assistant>\nVeo que tampoco se encontró el archivo `.monica-code/README.md`. Continuaré con los otros dos archivos de la lista:\n\n<tool>\nCould not find file .monica-code/LARGE-FILE-HANDLING-GUIDE.md\n\n<assistant>\nVeo que no se han encontrado tres de los archivos solicitados:\n- `.monica-code/AGENT-ANALYSIS.md`\n- `.monica-code/README.md`\n- `.monica-code/LARGE-FILE-HANDLING-GUIDE.md`\n\nAhora intentaré leer el archivo que mencionas como actualmente abierto:\n\n<tool>\nCould not find file .monica-code/AUTONOMOUS-AGENT-GUIDE.md\n\n", "completion": "No he podido encontrar ninguno de los cuatro archivos que me solicitaste leer:\n\n1. `.monica-code/AGENT-ANALYSIS.md` - No encontrado\n2. `.monica-code/README.md` - No encontrado\n3. `.monica-code/LARGE-FILE-HANDLING-GUIDE.md` - No encontrado\n4. `.monica-code/AUTONOMOUS-AGENT-GUIDE.md` - No encontrado\n\nEs posible que:\n- Las rutas no sean correctas\n- Los archivos estén en otra ubicación\n- Los archivos aún no existan y quizás deseas crearlos\n\n¿Quieres que intente buscar estos archivos en otra ubicación o prefieres que te ayude a crear alguno de ellos? También podría analizar el contenido del archivo index.d.ts que vimos anteriormente para entender mejor la estructura del proyecto si eso te resulta útil.", "completionOptions": {"model": "claude-3-7-sonnet-latest-thinking", "maxTokens": 4096, "tools": [{"type": "function", "function": {"name": "builtin_read_file", "description": "Use this tool whenever you need to view the contents of a file.", "parameters": {"type": "object", "required": ["filepath"], "properties": {"filepath": {"type": "string", "description": "The path of the file to read, relative to the root of the workspace."}}}}}, {"type": "function", "function": {"name": "builtin_create_new_file", "description": "Create a new file", "parameters": {"type": "object", "required": ["filepath", "contents"], "properties": {"filepath": {"type": "string", "description": "The path where the new file should be created"}, "contents": {"type": "string", "description": "The contents to write to the new file"}}}}}, {"type": "function", "function": {"name": "builtin_run_terminal_command", "description": "Run a terminal command in the current directory. The shell is not stateful and will not remember any previous commands.", "parameters": {"type": "object", "required": ["command"], "properties": {"command": {"type": "string", "description": "The command to run. This will be passed directly into the shell."}}}}}, {"type": "function", "function": {"name": "builtin_view_subdirectory", "description": "View the contents of a subdirectory", "parameters": {"type": "object", "required": ["directory_path"], "properties": {"directory_path": {"type": "string", "description": "The path of the subdirectory to view, relative to the root of the workspace"}}}}}, {"type": "function", "function": {"name": "builtin_view_repo_map", "description": "View the repository map", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_exact_search", "description": "Perform an exact file contents search over the repository using grep.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query to use. Must be a valid ripgrep regex expression, escaped where needed"}}}}}, {"type": "function", "function": {"name": "builtin_search_web", "description": "Performs a web search, returning top results. This tool should only be called for questions that require external knowledge. Common programming questions do not require web search.", "parameters": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "The natural language search query"}}}}}, {"type": "function", "function": {"name": "builtin_view_diff", "description": "View the current diff of working changes", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "builtin_read_currently_open_file", "description": "Read the currently open file in the IDE. If the user seems to be referring to a file that you can't see, this is probably it.", "parameters": {"type": "object", "properties": {}}}}]}}], "modelName": "Claude 3.7 Sonnet Thinking", "reasoningContent": "He intentado leer los cuatro archivos solicitados por el usuario, pero ninguno de ellos se encuentra en las rutas especificadas:\n\n1. `.monica-code/AGENT-ANALYSIS.md`\n2. `.monica-code/README.md`\n3. `.monica-code/LARGE-FILE-HANDLING-GUIDE.md`\n4. `.monica-code/AUTONOMOUS-AGENT-GUIDE.md`\n\nTodos los intentos devolvieron el mensaje \"Could not find file [ruta]\". Esto podría significar que:\n\n1. Los archivos no existen en las rutas proporcionadas\n2. Puede haber un error en las rutas (quizás falta algún directorio padre)\n3. Podría haber un problema de permisos\n\nDebo informar al usuario de esta situación y quizás preguntar si hay rutas alternativas o si desea que realice alguna otra acción.", "id": "097814b5-06f9-40d4-9d3b-20eaa34f1e22", "content": "No he podido encontrar ninguno de los cuatro archivos que me solicitaste leer:\n\n1. `.monica-code/AGENT-ANALYSIS.md` - No encontrado\n2. `.monica-code/README.md` - No encontrado\n3. `.monica-code/LARGE-FILE-HANDLING-GUIDE.md` - No encontrado\n4. `.monica-code/AUTONOMOUS-AGENT-GUIDE.md` - No encontrado\n\nEs posible que:\n- Las rutas no sean correctas\n- Los archivos estén en otra ubicación\n- Los archivos aún no existan y quizás deseas crearlos\n\n¿Quieres que intente buscar estos archivos en otra ubicación o prefieres que te ayude a crear alguno de ellos? También podría analizar el contenido del archivo index.d.ts que vimos anteriormente para entender mejor la estructura del proyecto si eso te resulta útil.", "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "createdAt": 1749065527252, "parentId": "74c7b989-3568-4bc3-a17b-7a912f1bfcaa", "role": "assistant"}], "conversation": {"id": "bbee8a98-76b3-49d5-89c3-e6124309a406", "workspaceDirectory": "c:\\Users\\<USER>\\.monica-code", "lastSavedAt": 1749065455262, "title": "/read-large-file index.d.ts ", "description": "No he podido encontrar ninguno de los cuatro archivos que me solicitaste leer:\n\n1. `.monica-code/AGE", "createdAt": 1749065409763, "updatedAt": 1749065527252}, "id": "bbee8a98-76b3-49d5-89c3-e6124309a406", "tree": {"activePath": ["a7bd7634-3277-489b-9857-665613930f0e", "279808ae-9c7d-4fce-aa28-0bcfe378b297", "68a7d992-82fc-4350-8d33-f07a2c66894d", "50e9ccc5-c08d-435f-b798-93669c96966e", "bd84222a-5065-48ef-837e-aea71774d3a0", "30cfdc44-3c04-48c4-a215-1f011fcb76f9", "3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d", "76a339c6-6f3b-469d-9313-8468b33ee1f5", "645a354b-2eaf-49a7-b862-ca436c6c45b7", "f7b3d91e-eafd-44f8-ae63-6ec5e83c2057", "74c7b989-3568-4bc3-a17b-7a912f1bfcaa", "097814b5-06f9-40d4-9d3b-20eaa34f1e22"], "conversationId": "bbee8a98-76b3-49d5-89c3-e6124309a406", "nodes": {"__root__": {"activeId": "a7bd7634-3277-489b-9857-665613930f0e", "childrenIds": ["a7bd7634-3277-489b-9857-665613930f0e"], "id": "__root__", "parentId": "__root_parent__"}, "a7bd7634-3277-489b-9857-665613930f0e": {"activeId": "279808ae-9c7d-4fce-aa28-0bcfe378b297", "childrenIds": ["279808ae-9c7d-4fce-aa28-0bcfe378b297"], "id": "a7bd7634-3277-489b-9857-665613930f0e", "parentId": "__root__"}, "279808ae-9c7d-4fce-aa28-0bcfe378b297": {"activeId": "68a7d992-82fc-4350-8d33-f07a2c66894d", "childrenIds": ["68a7d992-82fc-4350-8d33-f07a2c66894d"], "id": "279808ae-9c7d-4fce-aa28-0bcfe378b297", "parentId": "a7bd7634-3277-489b-9857-665613930f0e"}, "68a7d992-82fc-4350-8d33-f07a2c66894d": {"activeId": "50e9ccc5-c08d-435f-b798-93669c96966e", "childrenIds": ["50e9ccc5-c08d-435f-b798-93669c96966e"], "id": "68a7d992-82fc-4350-8d33-f07a2c66894d", "parentId": "279808ae-9c7d-4fce-aa28-0bcfe378b297"}, "50e9ccc5-c08d-435f-b798-93669c96966e": {"activeId": "bd84222a-5065-48ef-837e-aea71774d3a0", "childrenIds": ["bd84222a-5065-48ef-837e-aea71774d3a0"], "id": "50e9ccc5-c08d-435f-b798-93669c96966e", "parentId": "68a7d992-82fc-4350-8d33-f07a2c66894d"}, "bd84222a-5065-48ef-837e-aea71774d3a0": {"activeId": "30cfdc44-3c04-48c4-a215-1f011fcb76f9", "childrenIds": ["30cfdc44-3c04-48c4-a215-1f011fcb76f9"], "id": "bd84222a-5065-48ef-837e-aea71774d3a0", "parentId": "50e9ccc5-c08d-435f-b798-93669c96966e"}, "30cfdc44-3c04-48c4-a215-1f011fcb76f9": {"activeId": "3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d", "childrenIds": ["3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d"], "id": "30cfdc44-3c04-48c4-a215-1f011fcb76f9", "parentId": "bd84222a-5065-48ef-837e-aea71774d3a0"}, "3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d": {"activeId": "76a339c6-6f3b-469d-9313-8468b33ee1f5", "childrenIds": ["76a339c6-6f3b-469d-9313-8468b33ee1f5"], "id": "3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d", "parentId": "30cfdc44-3c04-48c4-a215-1f011fcb76f9"}, "76a339c6-6f3b-469d-9313-8468b33ee1f5": {"activeId": "645a354b-2eaf-49a7-b862-ca436c6c45b7", "childrenIds": ["645a354b-2eaf-49a7-b862-ca436c6c45b7"], "id": "76a339c6-6f3b-469d-9313-8468b33ee1f5", "parentId": "3e19a8b4-2368-4e7d-b6f4-0ebe4f14c30d"}, "645a354b-2eaf-49a7-b862-ca436c6c45b7": {"activeId": "f7b3d91e-eafd-44f8-ae63-6ec5e83c2057", "childrenIds": ["f7b3d91e-eafd-44f8-ae63-6ec5e83c2057"], "id": "645a354b-2eaf-49a7-b862-ca436c6c45b7", "parentId": "76a339c6-6f3b-469d-9313-8468b33ee1f5"}, "f7b3d91e-eafd-44f8-ae63-6ec5e83c2057": {"activeId": "74c7b989-3568-4bc3-a17b-7a912f1bfcaa", "childrenIds": ["74c7b989-3568-4bc3-a17b-7a912f1bfcaa"], "id": "f7b3d91e-eafd-44f8-ae63-6ec5e83c2057", "parentId": "645a354b-2eaf-49a7-b862-ca436c6c45b7"}, "74c7b989-3568-4bc3-a17b-7a912f1bfcaa": {"activeId": "097814b5-06f9-40d4-9d3b-20eaa34f1e22", "childrenIds": ["097814b5-06f9-40d4-9d3b-20eaa34f1e22"], "id": "74c7b989-3568-4bc3-a17b-7a912f1bfcaa", "parentId": "f7b3d91e-eafd-44f8-ae63-6ec5e83c2057"}, "097814b5-06f9-40d4-9d3b-20eaa34f1e22": {"activeId": null, "childrenIds": [], "id": "097814b5-06f9-40d4-9d3b-20eaa34f1e22", "parentId": "74c7b989-3568-4bc3-a17b-7a912f1bfcaa"}}, "rootNodeId": "__root__"}}