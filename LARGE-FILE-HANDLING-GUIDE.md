# Guía de Manejo de Archivos Grandes en Monica

## 📄 Configuración de Chunking Avanzado

Monica ahora está configurada para manejar archivos grandes mediante **chunking inteligente** que permite leer y analizar archivos que exceden los límites normales de procesamiento.

## ⚙️ Configuración de Indexación Optimizada

```json
{
  "indexingConfig": {
    "maxFileSize": 2000000,           // 2MB máximo por archivo
    "chunkSize": 2000,                // 2000 caracteres por chunk
    "chunkOverlap": 300,              // 300 caracteres de solapamiento
    "smartChunking": true,            // Chunking inteligente habilitado
    "chunkStrategy": "semantic",      // Estrategia semántica
    "preserveCodeBlocks": true,       // Preserva bloques de código
    "maxChunksPerFile": 50,           // Máximo 50 chunks por archivo
    "minChunkSize": 500               // Mínimo 500 caracteres por chunk
  }
}
```

## 🛠️ Herramientas para Archivos Grandes

### 1. **read_file_chunks**
Lee archivos grandes en chunks manejables:

```json
{
  "filepath": "path/to/large/file.js",
  "chunk_size": 2000,
  "overlap": 300,
  "start_chunk": 0,
  "max_chunks": 10
}
```

**Características:**
- ✅ **Solapamiento inteligente** - Evita cortar funciones o clases
- ✅ **Chunks configurables** - Tamaño ajustable según necesidades
- ✅ **Lectura parcial** - Puede leer solo secciones específicas
- ✅ **Preserva contexto** - Mantiene coherencia entre chunks

### 2. **analyze_large_codebase**
Analiza codebases completos con chunking:

```json
{
  "directory": "./src",
  "file_patterns": ["*.ts", "*.js", "*.py"],
  "max_file_size": 100000,
  "chunk_strategy": "semantic"
}
```

**Estrategias de Chunking:**
- **semantic** - Divide por significado semántico
- **function** - Divide por funciones/métodos
- **class** - Divide por clases/componentes
- **lines** - Divide por número de líneas

## 📋 Comandos para Archivos Grandes

### `/read-large-file`
```
Usa este comando para leer archivos que exceden los límites normales.
El agente automáticamente usará chunking para procesar el archivo completo.
```

**Ejemplo de uso:**
```
/read-large-file
Analiza el archivo src/components/LargeComponent.tsx que tiene más de 5000 líneas.
Proporciona un resumen completo de su estructura y funcionalidad.
```

### `/analyze-codebase`
```
Analiza todo el codebase usando chunking inteligente para manejar archivos grandes.
```

**Ejemplo de uso:**
```
/analyze-codebase
Examina todo el proyecto, incluyendo archivos grandes, 
y crea un reporte completo de la arquitectura.
```

## 🎯 Estrategias de Chunking

### **1. Chunking Semántico (Recomendado)**
- Divide el código respetando la estructura lógica
- Preserva funciones, clases y bloques completos
- Mantiene el contexto y las relaciones

### **2. Chunking por Funciones**
- Cada chunk contiene funciones completas
- Ideal para análisis de funcionalidad específica
- Preserva la lógica de cada función

### **3. Chunking por Clases**
- Cada chunk contiene clases completas
- Perfecto para análisis orientado a objetos
- Mantiene la encapsulación

### **4. Chunking por Líneas**
- División simple por número de líneas
- Más rápido pero puede cortar contexto
- Útil para archivos muy grandes

## 🔍 Casos de Uso Comunes

### **Caso 1: Archivo JavaScript/TypeScript Grande**
```
Tengo un archivo de 10,000 líneas con múltiples componentes React.
Usa /read-large-file para analizarlo y crear documentación.
```

**Resultado esperado:**
- Lectura en chunks de 2000 caracteres
- Análisis de cada componente
- Documentación completa generada
- Sugerencias de refactoring

### **Caso 2: Codebase Completo**
```
Analiza todo mi proyecto que tiene archivos muy grandes.
Usa /analyze-codebase para crear un reporte completo.
```

**Resultado esperado:**
- Análisis de todos los archivos
- Manejo automático de archivos grandes
- Reporte de arquitectura completo
- Identificación de patrones

### **Caso 3: Archivo de Configuración Extenso**
```
Tengo un archivo de configuración de 5MB.
Analízalo usando chunking y optimiza la configuración.
```

**Resultado esperado:**
- Lectura por secciones
- Análisis de cada configuración
- Optimizaciones sugeridas
- Documentación generada

## 📊 Límites y Optimizaciones

### **Límites Configurados:**
- **Tamaño máximo de archivo**: 2MB
- **Tamaño de chunk**: 2000 caracteres
- **Solapamiento**: 300 caracteres
- **Máximo chunks por archivo**: 50
- **Tamaño mínimo de chunk**: 500 caracteres

### **Optimizaciones Implementadas:**
- ✅ **Smart Chunking** - División inteligente
- ✅ **Code Block Preservation** - Preserva bloques de código
- ✅ **Semantic Awareness** - Comprende la estructura del código
- ✅ **Overlap Management** - Gestiona solapamiento eficientemente

## 🚀 Cómo Usar el Chunking

### **Método 1: Comandos Automáticos**
```
/read-large-file
[Especifica el archivo grande que quieres analizar]
```

### **Método 2: Prompt Directo**
```
Analiza el archivo [nombre] que es muy grande. 
Usa chunking para leerlo completamente y proporciona un análisis detallado.
```

### **Método 3: Análisis de Proyecto**
```
Examina todo mi proyecto, incluyendo archivos grandes, 
y crea documentación completa usando chunking cuando sea necesario.
```

## 🔧 Troubleshooting

### **Problema: Archivo demasiado grande**
**Solución:** Usa `/read-large-file` o ajusta `chunk_size` en la configuración

### **Problema: Chunks cortados incorrectamente**
**Solución:** Cambia `chunkStrategy` a "semantic" o "function"

### **Problema: Análisis incompleto**
**Solución:** Aumenta `maxChunksPerFile` o reduce `chunkSize`

### **Problema: Procesamiento lento**
**Solución:** Reduce `chunkOverlap` o aumenta `chunkSize`

## 📈 Beneficios del Chunking

1. **Manejo de archivos grandes** - Sin límites de tamaño
2. **Análisis completo** - Procesa todo el contenido
3. **Preservación de contexto** - Mantiene coherencia
4. **Flexibilidad** - Estrategias adaptables
5. **Eficiencia** - Procesamiento optimizado

¡Monica ahora puede manejar archivos de cualquier tamaño usando chunking inteligente! 📄✨
