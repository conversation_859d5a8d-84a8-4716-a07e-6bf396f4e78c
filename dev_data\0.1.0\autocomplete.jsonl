{"time":7,"completion":"// Add more models as needed\n  ","prefix":"{\n  \"models\": [\n    {\n      \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"GPT-4o (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Llama3.1 70b (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"llama3.1-70b\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Codestral (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"codestral-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    }\n    ","suffix":"\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Tab Autocomplete\",\n    \"provider\": \"free-trial\",\n    \"model\": \"codestral-latest\"\n  },\n  \"customCommands\": [\n    {\n      \"name\": \"test\",\n      \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n      \"description\": \"Write unit tests for highlighted code\"\n    }\n  ],\n  \"contextProviders\": [\n    {\n      \"name\": \"code\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"docs\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"diff\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"terminal\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"problems\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"folder\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"codebase\",\n      \"params\": {}\n    }","prompt":"[SUFFIX]\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Tab Autocomplete\",\n    \"provider\": \"free-trial\",\n    \"model\": \"codestral-latest\"\n  },\n  \"customCommands\": [\n    {\n      \"name\": \"test\",\n      \"prompt\": \"{{{ input }}}\\n\\nWrite a comprehensive set of unit tests for the selected code. It should setup, run tests that check for correctness including important edge cases, and teardown. Ensure that the tests are complete and sophisticated. Give the tests just as chat output, don't edit any file.\",\n      \"description\": \"Write unit tests for highlighted code\"\n    }\n  ],\n  \"contextProviders\": [\n    {\n      \"name\": \"code\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"docs\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"diff\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"terminal\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"problems\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"folder\",\n      \"params\": {}\n    },\n    {\n      \"name\": \"codebase\",\n      \"params\": {}\n    }[PREFIX]{\n  \"models\": [\n    {\n      \"title\": \"Claude 3.5 Sonnet (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"GPT-4o (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Llama3.1 70b (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"llama3.1-70b\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"title\": \"Codestral (Free Trial)\",\n      \"provider\": \"free-trial\",\n      \"model\": \"codestral-latest\",\n      \"systemMessage\": \"You are an expert software developer. You give helpful and concise responses.\"\n    },\n    {\n      \"model\": \"claude-3-5-sonnet-latest\",\n      \"provider\": \"anthropic\",\n      \"apiKey\": \"\",\n      \"title\": \"Claude 3.5 Sonnet\"\n    }\n    ","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Users\\<USER>\\.continue\\config.json","completionId":"2d13c598-c5a4-480f-9c17-98caeef93c4b","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732222568665,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.6,"recentlyEditedSimilarityThreshold":0,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":true}
{"time":1023,"completion":"True\r","prefix":"+++++ Layers.vbs\nisTrial = True\r\n\r\n\n+++++ Layers.vbs\n'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = ","suffix":"\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 10000\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 100000\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r","prompt":"[SUFFIX]\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 10000\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 100000\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r[PREFIX]+++++ Layers.vbs\nisTrial = True\r\n\r\n\n+++++ Layers.vbs\n'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = ","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Users\\<USER>\\Documents\\Cline\\Layers.vbs","completionId":"e1d33a5a-dd4a-445b-af31-09085b67cd92","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732326015907,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.6,"recentlyEditedSimilarityThreshold":0,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":true}
{"time":1325,"completion":"\t\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r","prefix":"'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 1000000","suffix":"\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 100000\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r\n\t\tOK = CreateLayerFromTcode(Dict, TCodeDescription, strLayer, strNodesLayer)\t\t' create layer per TCOD\r","prompt":"[SUFFIX]\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 100000\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r\n\t\tOK = CreateLayerFromTcode(Dict, TCodeDescription, strLayer, strNodesLayer)\t\t' create layer per TCOD\r[PREFIX]'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 1000000","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Users\\<USER>\\Documents\\Cline\\Layers.vbs","completionId":"22d32f84-aa9e-4d2f-b0c2-c80eb065251b","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732326127273,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.6,"recentlyEditedSimilarityThreshold":0,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":true}
{"time":1576,"completion":"\t\t' critical model size (number of elements) at which script asks to run fast version\r","prefix":"'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 1000000\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 10000000","suffix":"\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r\n\t\tOK = CreateLayerFromTcode(Dict, TCodeDescription, strLayer, strNodesLayer)\t\t' create layer per TCOD\r\n\t\tIf OK Then \r","prompt":"[SUFFIX]\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r\n\t\tOK = CreateLayerFromTcode(Dict, TCodeDescription, strLayer, strNodesLayer)\t\t' create layer per TCOD\r\n\t\tIf OK Then \r[PREFIX]'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 1000000\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 10000000","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Users\\<USER>\\Documents\\Cline\\Layers.vbs","completionId":"74cffb98-1fd8-4c29-baa1-8a3841cc8920","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732326132848,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.6,"recentlyEditedSimilarityThreshold":0,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":true}
{"time":1028,"completion":"\t\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r","prefix":"'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 100000000","suffix":"\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 10000000\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r","prompt":"[SUFFIX]\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = 10000000\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r[PREFIX]'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 100000000","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Users\\<USER>\\Documents\\Cline\\Layers.vbs","completionId":"58807935-a27f-4fd3-be75-c12099d16113","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732326168434,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.6,"recentlyEditedSimilarityThreshold":0,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":true}
{"time":985,"completion":"\t\t\t' critical model size at which script asks to run fast version\r","prefix":"'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 100000000\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = **********","suffix":"\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r\n\t\tOK = CreateLayerFromTcode(Dict, TCodeDescription, strLayer, strNodesLayer)\t\t' create layer per TCOD\r\n\t\tIf OK Then \r","prompt":"[SUFFIX]\t\t\t' element number at which script asks to run fast version\r\noptionGlobalMerge = True \t\t' set to True to perform a global merge \r\noptionRemoveProps = True \t\t' set to True to remove unused properties (might take some time)\r\n'==================================================================================================\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\nCall SetUpMoldflowClasses()\r\n\r\nDim response, boolCreateNodeLayers\r\nIf GetNumberOfElements > critModelSize Then \r\n\tresponse = MsgBox(\"Please choose:\" & vbCr & vbCr & \"Yes - faster, adds all nodes to a single Nodes layer\" & _\r\n\t\t\t   vbCr & \"No - slower, creates a separate node layer per property (depends on the number of non-part elements, not recommended for >200k elements)\",3, _\r\n\t\t\t   \"Select nodes layer option\")\r\n\tSelect Case response \r\n\t\tCase 7 boolCreateNodeLayers = True\r\n\t\tCase 6 boolCreateNodeLayers = False\r\n\t\tCase 2 WScript.Quit \r\n\tEnd Select\r\nElse \r\n\tboolCreateNodeLayers = True\r\nEnd If \r\n\r\nDim StartTime, EndTime\r\nStartTime = Timer()\r\nstrMessage = \"\"\r\n\r\n' ==================================================================================================\r\n' ========================================== FUNCTIONALITY =========================================\r\n' ==================================================================================================\r\n\r\n'.................................. create layers from standard TSETs ..............................\r\n' Create dictionaries for all TCode groups \r\nCall CreateTSETDictionary()\r\n\r\n' Creates layer containing CAD, curves, regions etc\r\nCall CreateGeometryLayer()\r\n\r\n' Create new layers and folders\r\nDim i, ii, Dict, OK, strLayers, strLayer, strNodesLayer, FolderName, TCodeDescription, strLayerInjection\r\nDim cntA, cntB\r\nFor cntA = 0 To DictTSETALL.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over folders \r\n\tSet Dict = DictTSETALL.Items()(cntA)\r\n\tFolderName = Dict.Keys()(0)\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t' first key is folder name \r\n\tIf FolderName = \"Nodes\" Then OK = CreateLayerFromAllNodes(strLayer)\t\t\t\t\t' put ALL nodes in one layer for now\r\n\tstrLayers = \"\"\r\n\tLayerManager.RemoveEmptyLayers()\r\n\tFolderManager.RemoveEmptyFolders()\r\n\tFor cntB = 1 To Dict.Count - 1\t\t\t\t\t\t\t\t\t\t\t\t\t\t' loop over TCodes\r\n\t\tTCodeDescription = Dict.Keys()(cntB)\r\n\t\tOK = CreateLayerFromTcode(Dict, TCodeDescription, strLayer, strNodesLayer)\t\t' create layer per TCOD\r\n\t\tIf OK Then \r[PREFIX]'%RunPerInstance\r\n' ###############################################################################################################\r\n' Created:  \t2022 by H. Schuette, Code Product Solutions\r\n'\t\t\t\twww.code-ps.com // <EMAIL>\r\n'\r\n' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.\r\n' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED\r\n' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED\r\n' ###############################################################################################################\r\nOption Explicit\r\nSetLocale(\"en-us\")\r\n\r\n' CHECK LICENSE =============================================================================\r\nDim isExternal, isTrial, varToday, endDate\r\nDim endDateY, endDateM, endDateD\r\n\r\nisExternal = True\r\nisTrial = False\r\n\r\nendDateY = \"2023\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateM = \"10\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nendDateD = \"23\"\t'variable to be used only in combination with the Moldflow Toolbox!\r\nvarToday = Date\r\nendDate = endDateY & \"/\" & endDateM & \"/\" & endDateD\t\t' Warning: M/D/Y can return MM.DD.YYYY !\r\n\r\nIf isTrial Then \r\n\tIf Not IsDate(endDate) Then endDate = endDateY & \"/\" & MonthName(endDateM,true) & \"/\" & endDateD\r\n\tIf Not IsDate(endDate) Then endDate = endDateD & \".\" & endDateM & \".\" & endDateY\r\n\tIf Not IsDate(endDate) Then \r\n\t\tMsgBox \"Problem with date format (\" & Date & \"). <NAME_EMAIL>. Thank you.\"\r\n\t\tWscript.Quit\r\n\tElse \r\n\t\tendDate = CDate(endDate)\r\n\tEnd If \r\n\tIf DateDiff(\"d\",varToday,endDate) < 0 Then\t' calculates date2 - date1 (is negative, if date1>date2)\r\n\t\tMsgBox \"Your trial has ended. Contact <EMAIL>\"\r\n\t\tWScript.Quit\r\n\tEnd If\r\nEnd If\r\n\r\n' ==================================================================================================\r\n' ============================================= SETUP ==============================================\r\n' ==================================================================================================\r\n\r\nDim FS\r\nSet FS = CreateObject(\"Scripting.FileSystemObject\")\r\n\r\n'.......................................... GLOBAL VARIABLES .......................................\r\n' classes\r\nDim Synergy\r\nDim SynergyGetter, SynergyVersion, StudyDoc, PropEd, LayerManager, _\r\nFolderManager, PredicateManager, MeshEditor\r\n\r\n' global variables\r\nDim DictTSET, DictTSetArray(), Prop, TriggerNodes(), EntList, strMessage, maxNodesLayerSize, critModelSize, _\r\noptionGlobalMerge, optionRemoveProps\r\nDim DictTSETALL, DictTSET0, DictTSET1, DictTSET2, DictTSET3, DictTSET4, DictTSET5, DictTSET6, _\r\nDictTSET7, DictTSET8, DictTSET9, DictTSET10\r\n\r\n'==================================================================================================\r\n'============================================ SETTINGS ============================================\r\n'assign initial variables\r\nmaxNodesLayerSize = 100000000\t\t' max. size of element layer to create own Node Layer (gives warning, if exceeded)\r\ncritModelSize = **********","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Users\\<USER>\\Documents\\Cline\\Layers.vbs","completionId":"078c89d2-b8fd-41ea-9a99-00a2f57f8be2","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732326177973,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.6,"recentlyEditedSimilarityThreshold":0,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":true}
{"time":370,"completion":"567890abcdefghi\" # Change this if you like\r\n\r\nLITELLM_DEBUG=true\r","prefix":"LITELLM_MASTER_KEY=\"sk-1234","suffix":"\" # Change this if you like\r\n\r\nOPENAI_API_KEY=\r\nGEMINI_API_KEY=\r\nANTHROPIC_API_KEY=\r\nMISTRAL_API_KEY=\r\nCODESTRAL_API_KEY=\r\nCOHERE_API_KEY=\r\nHUGGINGFACE_API_KEY=\r\nXAI_API_KEY=\r\nCEREBRAS_API_KEY=\r\nPERPLEXITYAI_API_KEY=\r\nGROQ_API_KEY=\r\nGITHUB_API_KEY=*********************************************************************************************\r\nDEEPSEEK_API_KEY=\r\nREPLICATE_API_KEY=\r\nTOGETHERAI_API_KEY=\r\nOPENROUTER_API_KEY=\r\n\r\n","prompt":"[SUFFIX]\" # Change this if you like\r\n\r\nOPENAI_API_KEY=\r\nGEMINI_API_KEY=\r\nANTHROPIC_API_KEY=\r\nMISTRAL_API_KEY=\r\nCODESTRAL_API_KEY=\r\nCOHERE_API_KEY=\r\nHUGGINGFACE_API_KEY=\r\nXAI_API_KEY=\r\nCEREBRAS_API_KEY=\r\nPERPLEXITYAI_API_KEY=\r\nGROQ_API_KEY=\r\nGITHUB_API_KEY=*********************************************************************************************\r\nDEEPSEEK_API_KEY=\r\nREPLICATE_API_KEY=\r\nTOGETHERAI_API_KEY=\r\nOPENROUTER_API_KEY=\r\n\r\n[PREFIX]LITELLM_MASTER_KEY=\"sk-1234","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":true,"filepath":"c:\\Webui_LiteLLM\\.env","completionId":"5199701d-d449-424f-9b01-e4e1c553a376","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732492150700,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":1216,"completion":" # Add more models as needed, following the same format.","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   ","suffix":"- model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY","prompt":"[SUFFIX]- model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   ","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"3b8952b8-93c6-4494-9a57-69c790de7d17","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732493643768,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":1601,"completion":"-13b-v1\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-beta","suffix":"\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY","prompt":"[SUFFIX]\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-beta","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"78b975ab-0f39-4426-afb9-d41df4f4f618","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732493655602,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":617,"completion":"eta\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-B","suffix":"eta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY","prompt":"[SUFFIX]eta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-B","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"db80e49f-834d-4beb-9b11-9037a9d4150b","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732493684187,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":6,"completion":"_API_KEY\r\n  - model_name: Mistral-7B-Instruct-v0.1\r\n    litellm_params:\r\n      model: mistral/mistral-7b-instruct-v0.1\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/XAI","suffix":"_API_KEY","prompt":"[SUFFIX]_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-small-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/XAI","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":true,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"50e4b2b8-d742-4679-ad4d-549daa24f667","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732493711654,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":1239,"completion":"-2411\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large","suffix":"-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"60ac68e6-36a6-4da2-8507-302581c787bf","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732493850455,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":1123,"completion":"2411","prefix":"+++++ litellm_config.yaml\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n\n+++++ litellm_config.yaml\nmodel_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-","suffix":"\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n   - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]+++++ litellm_config.yaml\n      model: mistral/mistral-large-2411\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n\n+++++ litellm_config.yaml\nmodel_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-latest\r\n    litellm_params:\r\n      model: mistral/mistral-large-","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"b43df4f1-d4bc-4990-a3cf-d8b400fc0234","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732493855524,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":2,"completion":"github/llama3.2-90B-vision-chat\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/","suffix":"mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: Grok-Beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":true,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"ad568439-8912-4ee7-9935-5f73b2083d46","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495186690,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":936,"completion":"xai/grok-beta\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/","suffix":"Grok-Beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]Grok-Beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"65244f74-ad71-4d55-a147-ea8ecae61162","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495195559,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":1019,"completion":"pt-4-vision-preview\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/g","suffix":"rok-Beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]rok-Beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/g","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"009ab1a1-aecc-41d8-9f23-b7571b77ea97","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495200410,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":741,"completion":"pt4-vision\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/grok-g","suffix":"eta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]eta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/grok-g","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"1931f069-47cf-489e-a09b-287a89e8a679","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495201662,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":1099,"completion":"litellm/mistral/mistral-large-2407\r","prefix":"+++++ litellm_config.yaml\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n\n+++++ litellm_config.yaml\nmodel_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: ","suffix":"mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]+++++ litellm_config.yaml\n  - model_name: litellm/mistral/mistral-large-2407\r\n    litellm_params:\r\n\n+++++ litellm_config.yaml\nmodel_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: ","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"fab79406-5049-4dd4-a8fe-b29669807c99","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495381068,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":868,"completion":"mistral/","prefix":"+++++ litellm_config.yaml\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n\n+++++ litellm_config.yaml\nmodel_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/","suffix":"mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY","prompt":"[SUFFIX]mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: litellm/grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY[PREFIX]+++++ litellm_config.yaml\n      model: mistral/mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n\n+++++ litellm_config.yaml\nmodel_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"626ddff6-7f6b-4673-a01d-ffb976c2294d","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495398391,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":3,"completion":"\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n      - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY","suffix":"","prompt":"[SUFFIX][PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n      - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"60b2c814-ac16-41da-abbb-420cf4636051","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495621290,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":4,"completion":"alpha\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n  - model_name: codestral-","suffix":"\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY","prompt":"[SUFFIX]\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n  - model_name: codestral-","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":true,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"6959ce1a-1ed4-47fd-9c37-787fcf5d83e1","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495642347,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":6,"completion":"TRAL_API_KEY\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: mcodestral/modestral-latest\r\n      api_key: os.environ/CODES","suffix":"TRAL_API_KEY","prompt":"[SUFFIX]TRAL_API_KEY[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: mcodestral/modestral-latest\r\n      api_key: os.environ/CODES","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":true,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"2420ac08-ea56-4a08-a4f6-e2b4550069e5","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495681231,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":5,"completion":"\r\n  - model_name: litellm/anthropic/claude-2-100k\r\n    litellm_params:\r\n      model: anthropic/claude-2-100k\r\n      api_key: os.environ/ANTHROPIC_API_KEY\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: mcodestral/modestral-latest\r\n      api_key: os.environ/CODESTRAL_API_KEY\r\n      ","suffix":"","prompt":"[SUFFIX][PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: os.environ/XAI_API_KEY\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: mcodestral/modestral-latest\r\n      api_key: os.environ/CODESTRAL_API_KEY\r\n      ","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"6cd29a44-3412-4868-a4d4-6be2addd5d77","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732495757867,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":11,"completion":"\r\n  - model_name: llama-2-70b-chat-hf\r\n    litellm_params:\r\n      model: meta-llama/Llama-2-70b-chat-hf\r\n      api_key: os.environ/HF_API_KEY\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: ************************************************************************************\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: codestral/codestral-latest\r\n      api_key: ocFkapRR7Efu1sfnQ0akqzgAA66KU9zs","suffix":"\r\n","prompt":"[SUFFIX]\r\n[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: os.environ/MISTRAL_API_KEY\r\n  - model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: ************************************************************************************\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: codestral/codestral-latest\r\n      api_key: ocFkapRR7Efu1sfnQ0akqzgAA66KU9zs","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":true,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"b114cd3d-4b74-4303-88f6-35431f3c8875","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732496520020,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":3,"completion":"\\/7\\/7\\/7\\/7\\/7\\/7\\/7\\/7\r\n  - model_name: mistral-medium-2407\r","prefix":"model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: oXfeFh79plKmcdMWwx2kUVBDTMV9Og7ey","suffix":"- model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: ************************************************************************************\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: codestral/codestral-latest\r\n      api_key: ocFkapRR7Efu1sfnQ0akqzgAA66KU9zs\r\n","prompt":"[SUFFIX]- model_name: grok-beta\r\n    litellm_params:\r\n      model: xai/grok-beta\r\n      api_key: ************************************************************************************\r\n  - model_name: codestral-latest\r\n    litellm_params:\r\n      model: codestral/codestral-latest\r\n      api_key: ocFkapRR7Efu1sfnQ0akqzgAA66KU9zs\r\n[PREFIX]model_list:\r\n  - model_name: litellm/github/gpt-4o\r\n    litellm_params:\r\n      model: github/gpt-4o\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: litellm/github/llama3.2-90B-vision-instruct\r\n    litellm_params:\r\n      model: github/llama-3.2-90b-vision-instruct\r\n      api_key: os.environ/GITHUB_API_KEY\r\n  - model_name: mistral-large-2407\r\n    litellm_params:\r\n      model: mistral/mistral-large-2407\r\n      api_key: oXfeFh79plKmcdMWwx2kUVBDTMV9Og7ey","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"c:\\Webui_LiteLLM\\litellm_config.yaml","completionId":"37d64b13-aa22-4821-8148-ed9260027ff6","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1732496694711,"disable":false,"useCopyBuffer":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.85,"maxSuffixPercentage":0.25,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"maxSnippetPercentage":0.5,"maxDiffPercentage":0.5,"useCache":true,"onlyMyCode":true,"useOtherFiles":true,"useRecentlyEdited":true,"recentLinePrefixMatchMinLength":7,"useImports":true,"useRootPathContext":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
{"time":1029,"completion":"\n\u000e","prefix":"o\tf.K~(%*^<\u0013d6E<}!.A|\t\u00104\u0003,73$\u0015\u000fm\u0015F>\u000et=8\u0018_gcu\r\n6y)%8*J(f\bKxs?\u0014\u000b.P\u0001jH\u001a\b\u001a(1\u001b57Y+:%k\t*r%aV8-*0VoQ!a\u001f+rgfH<\u0013cn\r\n:%c!\u000e:\u0001 &\u0004\u0001- \"F>W|F/\u000eZ\u000e\u0002\u0013lCbY/}z8,BpM\r\n\u000fNgxl>Oi\u0004wlGnw><\bd\u001c(?QR\u001f-h\r\n$_'s\u0006xoe\u000e\u001f|P? Hq/d'\r\ncd sharedv","suffix":"UqNn<T>i,]i\f!3\u0013T81?BnB(t\u0000}r<!\u0013r^&d\u0003klZd@f\u0007'dIyi#%V9\u0004v4\u0015x>>6\u0012'\u0006K4F7xdd\u00148Ls:\r\nfsvpY{K;lZ\u0015:whQ<_o=A4k2Ao\u0019o%\u0001*=+=J=KLa\u00132vq0\u0012sK&o\u000fhf\"v\u0012d\u000ftm-\u0000KL\u0002}\u0000,[\r\nt[\u0004\u000e\u0004/\u0001)ao\u0014#\u001fd%Zk\r\n\u0007\u0001&XOH[-Ao\u001d_kZ\bRByRaQ\u000fr\u000b\u000eY\u0001oI$\u000e\u00187\u0017\r","prompt":"[SUFFIX]UqNn<T>i,]i\f!3\u0013T81?BnB(t\u0000}r<!\u0013r^&d\u0003klZd@f\u0007'dIyi#%V9\u0004v4\u0015x>>6\u0012'\u0006K4F7xdd\u00148Ls:\r\nfsvpY{K;lZ\u0015:whQ<_o=A4k2Ao\u0019o%\u0001*=+=J=KLa\u00132vq0\u0012sK&o\u000fhf\"v\u0012d\u000ftm-\u0000KL\u0002}\u0000,[\r\nt[\u0004\u000e\u0004/\u0001)ao\u0014#\u001fd%Zk\r\n\u0007\u0001&XOH[-Ao\u001d_kZ\bRByRaQ\u000fr\u000b\u000eY\u0001oI$\u000e\u00187\u0017\r[PREFIX]o\tf.K~(%*^<\u0013d6E<}!.A|\t\u00104\u0003,73$\u0015\u000fm\u0015F>\u000et=8\u0018_gcu\r\n6y)%8*J(f\bKxs?\u0014\u000b.P\u0001jH\u001a\b\u001a(1\u001b57Y+:%k\t*r%aV8-*0VoQ!a\u001f+rgfH<\u0013cn\r\n:%c!\u000e:\u0001 &\u0004\u0001- \"F>W|F/\u000eZ\u000e\u0002\u0013lCbY/}z8,BpM\r\n\u000fNgxl>Oi\u0004wlGnw><\bd\u001c(?QR\u001f-h\r\n$_'s\u0006xoe\u000e\u001f|P? Hq/d'\r\ncd sharedv","modelProvider":"free-trial","modelName":"codestral-latest","completionOptions":{"stop":["[PREFIX]","[SUFFIX]","/src/","#- coding: utf-8","```"]},"cacheHit":false,"filepath":"file:///c%3A/Users/<USER>/Documents/Synera/AnsaConnector/ANSA_Solid_Mesh.pyb","completionId":"0b58139d-909d-4be6-8a80-b17db6e4c3bf","uniqueId":"eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e","timestamp":1738367946162,"disable":false,"useFileSuffix":true,"maxPromptTokens":1024,"prefixPercentage":0.3,"maxSuffixPercentage":0.2,"debounceDelay":350,"multilineCompletions":"auto","slidingWindowPrefixPercentage":0.75,"slidingWindowSize":500,"useCache":true,"onlyMyCode":true,"useRecentlyEdited":true,"useImports":true,"transform":true,"showWhateverWeHaveAtXMs":300,"accepted":false}
