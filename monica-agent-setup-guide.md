# Guía de Configuración: Modo Agente en Monica Code

## ✅ Cambios Realizados

He modificado tu configuración de Monica para habilitar el modo agente. Los cambios incluyen:

### 1. **Modelos LLM Configurados** (`config.json`)
```json
{
  "models": [
    {
      "title": "GPT-4o (Local)",
      "provider": "openai",
      "model": "gpt-4o",
      "apiKey": "YOUR_OPENAI_API_KEY_HERE"
    },
    {
      "title": "Claude 3.5 Sonnet (Local)", 
      "provider": "anthropic",
      "model": "claude-3-5-sonnet-20241022",
      "apiKey": "YOUR_ANTHROPIC_API_KEY_HERE"
    },
    {
      "title": "GPT-3.5 Turbo (Fast)",
      "provider": "openai", 
      "model": "gpt-3.5-turbo",
      "apiKey": "YOUR_OPENAI_API_KEY_HERE"
    }
  ]
}
```

### 2. **Autocompletado Configurado**
```json
{
  "tabAutocompleteModel": {
    "title": "Codestral Autocomplete",
    "provider": "mistral",
    "model": "codestral-latest",
    "apiKey": "YOUR_MISTRAL_API_KEY_HERE"
  }
}
```

### 3. **Embeddings y Reranking**
```json
{
  "embeddingsProvider": {
    "provider": "openai",
    "model": "text-embedding-3-small",
    "apiKey": "YOUR_OPENAI_API_KEY_HERE"
  },
  "reranker": {
    "name": "voyage",
    "params": {
      "apiKey": "YOUR_VOYAGE_API_KEY_HERE"
    }
  }
}
```

### 4. **Modo Agente Habilitado**
```json
{
  "disableIndexing": false,
  "experimental": {
    "useTools": true,
    "modelRoles": {
      "inlineEdit": "GPT-4o (Local)",
      "applyCodeBlock": "Claude 3.5 Sonnet (Local)"
    }
  },
  "tools": [
    // Herramientas para edit_file, create_file, run_command
  ]
}
```

### 5. **Indexación Habilitada** (`.continuerc.json`)
```json
{"disableIndexing": false, "mergeBehavior": "merge"}
```

## 🔧 Pasos Siguientes

### **Paso 1: Configurar API Keys**

Reemplaza los placeholders con tus API keys reales:

#### **OpenAI** (Requerido para GPT-4o, GPT-3.5, embeddings)
```json
"apiKey": "sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

#### **Anthropic** (Opcional, para Claude)
```json
"apiKey": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

#### **Mistral** (Opcional, para autocompletado)
```json
"apiKey": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

#### **Voyage AI** (Opcional, para reranking)
```json
"apiKey": "pa-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

### **Paso 2: Reiniciar Monica Code**

1. Cierra VS Code completamente
2. Abre VS Code de nuevo
3. Monica debería detectar la nueva configuración automáticamente

### **Paso 3: Verificar Funcionamiento**

#### **Probar Chat con Modelos Locales:**
1. Abre el panel de Monica (Ctrl+L)
2. Verifica que aparezcan tus modelos configurados en el selector
3. Haz una pregunta simple para probar

#### **Probar Modo Agente:**
1. Selecciona código en un archivo
2. Usa un prompt como: "Analiza este código y sugiere mejoras"
3. El agente debería poder usar herramientas para editar archivos

#### **Probar Autocompletado:**
1. Empieza a escribir código
2. Deberías ver sugerencias automáticas

### **Paso 4: Configuración Opcional**

#### **Si no tienes todas las API keys:**

**Solo OpenAI:**
```json
{
  "models": [
    {
      "title": "GPT-4o",
      "provider": "openai",
      "model": "gpt-4o",
      "apiKey": "tu-openai-key"
    }
  ],
  "tabAutocompleteModel": {
    "title": "GPT-3.5 Autocomplete",
    "provider": "openai", 
    "model": "gpt-3.5-turbo",
    "apiKey": "tu-openai-key"
  },
  "embeddingsProvider": {
    "provider": "openai",
    "model": "text-embedding-3-small",
    "apiKey": "tu-openai-key"
  }
}
```

**Con Ollama (modelos locales gratuitos):**
```json
{
  "models": [
    {
      "title": "Llama 3.1 Local",
      "provider": "ollama",
      "model": "llama3.1:8b"
    }
  ],
  "tabAutocompleteModel": {
    "title": "CodeLlama Autocomplete",
    "provider": "ollama",
    "model": "codellama:7b"
  }
}
```

## 🎯 Funcionalidades del Modo Agente

Una vez configurado, tendrás acceso a:

### **1. Agente Conversacional**
- Chat inteligente con tu codebase
- Análisis de código completo
- Sugerencias de arquitectura

### **2. Herramientas de Agente**
- **edit_file**: Editar archivos específicos
- **create_file**: Crear nuevos archivos
- **run_command**: Ejecutar comandos shell

### **3. Capacidades Avanzadas**
- Refactoring automático
- Generación de tests
- Análisis de dependencias
- Detección de bugs

### **4. Context Providers Mejorados**
- Indexación completa del codebase
- Búsqueda semántica
- Análisis de relaciones entre archivos

## 🚨 Troubleshooting

### **Problema: No aparecen los modelos**
- Verifica que las API keys sean correctas
- Reinicia VS Code completamente
- Revisa la consola de desarrollador (F12) para errores

### **Problema: Agente no funciona**
- Asegúrate que `"useTools": true` esté configurado
- Verifica que la indexación esté habilitada
- Comprueba que los modelos tengan suficiente contexto

### **Problema: Autocompletado lento**
- Usa un modelo más rápido (GPT-3.5 en lugar de GPT-4)
- Ajusta `temperature` a 0.1 para respuestas más deterministas
- Reduce `maxTokens` para respuestas más cortas

## 📝 Próximos Pasos

1. **Configura tus API keys** en el archivo `config.json`
2. **Reinicia VS Code**
3. **Prueba las funcionalidades** paso a paso
4. **Ajusta la configuración** según tus necesidades

¿Tienes alguna pregunta sobre la configuración o necesitas ayuda con algún paso específico?
