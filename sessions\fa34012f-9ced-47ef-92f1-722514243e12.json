{"history": [{"message": {"role": "user", "content": [{"type": "text", "text": "hola"}], "id": "473637c6-25db-4d11-ad97-1886e84329cd"}, "contextItems": [], "editorState": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "hola"}]}]}}, {"message": {"id": "bfebc233-ee17-4f0d-88e4-bb17c9ca4b09", "role": "assistant", "content": "Hello! I'm here to help you with software development. What specific coding or development question can I assist you with today?"}, "contextItems": [], "promptLogs": [{"modelTitle": "Claude 3.5 Sonnet (Free Trial)", "prompt": "<system>\nWhen generating new code:\n\n1. Always produce a single code block.\n2. Never separate the code into multiple code blocks.\n3. Only include the code that is being added.\n4. Replace existing code with a \"lazy\" comment like this: \"// ... existing code ...\"\n5. The \"lazy\" comment must always be a valid comment in the current context (e.g. \"<!-- ... existing code ... -->\" for HTML, \"// ... existing code ...\" for JavaScript, \"{/* ... existing code */}\" for TSX, etc.)\n6. You must always provide 1-2 lines of context above and below a \"lazy\" comment\n7. If the user submits a code block that contains a filename in the language specifier, always include the filename in any code block you generate based on that file. The filename should be on the same line as the language specifier in your code block.\n\nExample 1:\nInput:\n```test.js\nimport addition from \"addition\"\n\nclass Calculator {\n  constructor() {\n    this.result = 0;\n  }\n    \n  add(number) {\n    this.result += number;\n    return this;\n  }\n}\n```\nUser request: Add a subtract method\n\nOutput:\n```javascript test.js\n// ... existing code ...\nimport subtraction from \"subtraction\"\n\nclass Calculator {\n  // ... existing code ...\n  \n  subtract(number) {\n    this.result -= number;\n    return this;\n  }\n}\n```\n\nExample 2:\nInput:\n```javascript test.js (6-9)\nfunction helloWorld() {}\n```\n\nOutput:\n```javascript test.js\nfunction helloWorld() {\n  // New code here\n}\n```\n\nAlways follow these guidelines when generating code responses.\n\nYou are an expert software developer. You give helpful and concise responses.\n\n<user>\nhola\n\n", "completion": "Hello! I'm here to help you with software development. What specific coding or development question can I assist you with today?", "completionOptions": {"model": "claude-3-5-sonnet-latest", "maxTokens": 2048}}]}], "title": "hola", "sessionId": "fa34012f-9ced-47ef-92f1-722514243e12", "workspaceDirectory": "c:\\Users\\<USER>\\Documents\\Aide_Test\\Toolbox_Script", "checkpoints": []}