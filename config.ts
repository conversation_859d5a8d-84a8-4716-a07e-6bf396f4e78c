export function modifyConfig(config: Config): Config {
  // <PERSON><PERSON><PERSON> servidores MCP
  config.experimental = config.experimental || {};
  config.experimental.modelContextProtocolServers = [
    {
      name: "code-mcp",
      transport: {
        type: "stdio",
        command: "npx",
        args: [
          "-y",
          "@smithery/cli@latest",
          "run",
          "@block/code-mcp",
          "--key",
          "b2457e93-85f8-4f96-802a-e5a558c5177d"
        ]
      }
    },
    {
      name: "desktop-commander",
      transport: {
        type: "stdio",
        command: "cmd",
        args: [
          "/c",
          "npx",
          "-y",
          "@smithery/cli@latest",
          "run",
          "@wonderwhy-er/desktop-commander",
          "--key",
          "b2457e93-85f8-4f96-802a-e5a558c5177d"
        ]
      }
    },
    {
      name: "memory-bank-mcp",
      transport: {
        type: "stdio",
        command: "cmd",
        args: [
          "/c",
          "npx",
          "-y",
          "@smithery/cli@latest",
          "run",
          "@alioshr/memory-bank-mcp",
          "--key",
          "b2457e93-85f8-4f96-802a-e5a558c5177d",
          "--profile",
          "democratic-python-XxE9Yj"
        ]
      }
    },
    {
      name: "server-sequential-thinking",
      transport: {
        type: "stdio",
        command: "cmd",
        args: [
          "/c",
          "npx",
          "-y",
          "@smithery/cli@latest",
          "run",
          "@smithery-ai/server-sequential-thinking",
          "--key",
          "b2457e93-85f8-4f96-802a-e5a558c5177d"
        ]
      }
    },
    {
      name: "Office-PowerPoint-MCP-Server",
      transport: {
        type: "stdio",
        command: "cmd",
        args: [
          "/c",
          "npx",
          "-y",
          "@smithery/cli@latest",
          "run",
          "@GongRzhe/Office-PowerPoint-MCP-Server",
          "--key",
          "b2457e93-85f8-4f96-802a-e5a558c5177d"
        ]
      }
    },
    {
      name: "excel-mcp-server",
      transport: {
        type: "stdio",
        command: "cmd",
        args: [
          "/c",
          "npx",
          "-y",
          "@smithery/cli@latest",
          "run",
          "@negokaz/excel-mcp-server",
          "--key",
          "b2457e93-85f8-4f96-802a-e5a558c5177d"
        ]
      }
    }
  ];

  return config;
}