{"prefix": "{\n  \"models\": [\n    {\n      \"title\": \"GPT-4o Monica\",\n      \"provider\": \"openai\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"Eres un asistente de desarrollo experto especializado en análisis y mejora de código. Tu objetivo es proporcionar sugerencias precisas y prácticas que mejoren la calidad, eficiencia y legibilidad del código.\"\n    },\n    {\n      \"title\": \"Claude 3.7 Sonnet Latest\",\n      \"provider\": \"anthropic\",\n      \"model\": \"claude-3-7-sonnet-latest\",\n      \"systemMessage\": \"<PERSON><PERSON>, un asistente de programación experto desarrollado por Anthropic. Especializas en análisis profundo de código, explicaciones detalladas y generación de soluciones elegantes.\"\n    },\n    {\n      \"title\": \"Claude 3.7 Sonnet Thinking\",\n      \"provider\": \"anthropic\",\n      \"model\": \"claude-3-7-sonnet-latest-thinking\",\n      \"systemMessage\": \"Eres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones.\"\n    },\n    {\n      \"title\": \"Claude 3.5 Sonnet\",\n      \"provider\": \"anthropic\",\n      \"model\": \"claude-3-5-sonnet\",\n      \"systemMessage\": \"Eres un asistente de programación experto. Tu especialidad es entender rápidamente problemas complejos y ofrecer soluciones concretas y prácticas.\"\n    },", "suffix": "\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Fast Autocomplete\",\n    \"provider\": \"openai\",\n    \"model\": \"gpt-3.5-turbo\",\n    \"temperature\": 0.1,\n    \"maxTokens\": 100\n  },\n  \"embeddingsProvider\": {\n    \"provider\": \"free-trial\"\n  },\n  \"reranker\": {\n    \"name\": \"free-trial\"\n  },\n  \"rules\": [\n    \"Always analyze multiple related files when making changes to understand the full context\",\n    \"Automatically create documentation (README.md, CHANGELOG.md) when creating new projects or major changes\",\n    \"When editing code, always check for related files (tests, configs, docs) that might need updates\",\n    \"Create session summaries in markdown format after complex multi-file operations\",\n    \"Use semantic search across the codebase before making assumptions about existing functionality\",\n    \"Always validate configuration changes by checking related files and dependencies\",\n    \"When creating new files, follow the existing project structure and naming conventions\",\n    \"Proactively suggest improvements and optimizations when analyzing code\",\n    \"Create comprehensive documentation for any new features or significant changes\",\n    \"Always consider the impact of changes on the entire project ecosystem\",\n    \"Use tools autonomously to implement changes across multiple files when beneficial\",\n    \"Maintain consistency in code style and patterns across the entire project\"\n  ],\n  \"customCommands\": [\n    {\n      \"name\": \"test\",", "prompt": "[SUFFIX]\n  ],\n  \"tabAutocompleteModel\": {\n    \"title\": \"Fast Autocomplete\",\n    \"provider\": \"openai\",\n    \"model\": \"gpt-3.5-turbo\",\n    \"temperature\": 0.1,\n    \"maxTokens\": 100\n  },\n  \"embeddingsProvider\": {\n    \"provider\": \"free-trial\"\n  },\n  \"reranker\": {\n    \"name\": \"free-trial\"\n  },\n  \"rules\": [\n    \"Always analyze multiple related files when making changes to understand the full context\",\n    \"Automatically create documentation (README.md, CHANGELOG.md) when creating new projects or major changes\",\n    \"When editing code, always check for related files (tests, configs, docs) that might need updates\",\n    \"Create session summaries in markdown format after complex multi-file operations\",\n    \"Use semantic search across the codebase before making assumptions about existing functionality\",\n    \"Always validate configuration changes by checking related files and dependencies\",\n    \"When creating new files, follow the existing project structure and naming conventions\",\n    \"Proactively suggest improvements and optimizations when analyzing code\",\n    \"Create comprehensive documentation for any new features or significant changes\",\n    \"Always consider the impact of changes on the entire project ecosystem\",\n    \"Use tools autonomously to implement changes across multiple files when beneficial\",\n    \"Maintain consistency in code style and patterns across the entire project\"\n  ],\n  \"customCommands\": [\n    {\n      \"name\": \"test\",[PREFIX]{\n  \"models\": [\n    {\n      \"title\": \"GPT-4o Monica\",\n      \"provider\": \"openai\",\n      \"model\": \"gpt-4o\",\n      \"systemMessage\": \"Eres un asistente de desarrollo experto especializado en análisis y mejora de código. Tu objetivo es proporcionar sugerencias precisas y prácticas que mejoren la calidad, eficiencia y legibilidad del código.\"\n    },\n    {\n      \"title\": \"Claude 3.7 Sonnet Latest\",\n      \"provider\": \"anthropic\",\n      \"model\": \"claude-3-7-sonnet-latest\",\n      \"systemMessage\": \"Eres Claude, un asistente de programación experto desarrollado por Anthropic. Especializas en análisis profundo de código, explicaciones detalladas y generación de soluciones elegantes.\"\n    },\n    {\n      \"title\": \"Claude 3.7 Sonnet Thinking\",\n      \"provider\": \"anthropic\",\n      \"model\": \"claude-3-7-sonnet-latest-thinking\",\n      \"systemMessage\": \"Eres Claude en modo thinking. Analiza problemas paso a paso, explicando tu razonamiento detalladamente. Prioriza la precisión y claridad en tus explicaciones.\"\n    },\n    {\n      \"title\": \"Claude 3.5 Sonnet\",\n      \"provider\": \"anthropic\",\n      \"model\": \"claude-3-5-sonnet\",\n      \"systemMessage\": \"Eres un asistente de programación experto. Tu especialidad es entender rápidamente problemas complejos y ofrecer soluciones concretas y prácticas.\"\n    },", "modelProvider": "monica", "modelName": "codestral-latest", "completionOptions": {"stop": ["[PREFIX]", "[SUFFIX]"]}, "filepath": "c:\\Users\\<USER>\\.monica-code\\config.json", "completionId": "6951e382-30ce-45ac-8eff-0f03cac107e7", "uniqueId": "eb4cb91d2f88387f690fd3dbabac76bcf2fb60a095e31154c6cf07fe4fbd184e", "disable": false, "useCopyBuffer": false, "useFileSuffix": true, "maxPromptTokens": 1500, "prefixPercentage": 0.85, "maxSuffixPercentage": 0.25, "debounceDelay": 300, "multilineCompletions": "auto", "slidingWindowPrefixPercentage": 0.75, "slidingWindowSize": 500, "maxSnippetPercentage": 0.6, "recentlyEditedSimilarityThreshold": 0.3, "useCache": true, "onlyMyCode": true, "useOtherFiles": true, "useRecentlyEdited": true, "recentLinePrefixMatchMinLength": 7, "useImports": true, "useRootPathContext": true, "cacheHit": false, "completion": "\n    {\n      \"title\": \"Claude 3.5 Sonnet Thinking\",\n      \"provider\": \"anthropic\",\n      \"model\": \"claude-3-5-sonnet-thinking\",\n      \"systemMessage\": \"Eres un asistente de programación experto. Tu especialidad es entender rápidamente problemas complejos y ofrecer soluciones concretas y prácticas.\"", "time": 808, "accepted": false}