{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "System", "moduleResolution": "Node", "noEmit": false, "noEmitOnError": false, "outFile": "./out/config.js", "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["./config.ts"]}