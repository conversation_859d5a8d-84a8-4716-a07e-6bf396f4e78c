# Continue.dev - Guía de Configuración

## Descripción General

Continue.dev es un asistente de código AI de código abierto que permite a los desarrolladores crear, compartir y usar asistentes de código AI personalizados. Es compatible con VS Code y JetBrains IDEs, ofreciendo funcionalidades como chat, autocompletado, edición y agente AI.

## Arquitectura del Proyecto

### Estructura del Repositorio
```
continuedev/continue/
├── .continue/          # Configuración por defecto
├── core/              # Lógica central del asistente
├── extensions/        # Extensiones para IDEs
│   ├── vscode/       # Extensión de VS Code
│   └── jetbrains/    # Plugin de JetBrains
├── gui/              # Interfaz de usuario
├── docs/             # Documentación
├── packages/         # Paquetes compartidos
└── binary/           # Binarios compilados
```

### Tecnologías Principales
- **Lenguajes**: TypeScript (70.9%), JavaScript (15.7%), <PERSON><PERSON><PERSON> (6.7%), Python (2.4%)
- **Framework**: Node.js con extensiones para IDEs
- **Licencia**: Apache 2.0

## Configuración Principal

### Formatos de Configuración

#### 1. YAML Config (Recomendado)
**Ubicación**: 
- `~/.continue/config.yaml` (macOS/Linux)
- `%USERPROFILE%\.continue\config.yaml` (Windows)

**Estructura básica**:
```yaml
name: MyProject
version: 0.0.1
schema: v1

models:
  - name: GPT-4o
    provider: openai
    model: gpt-4o
    roles:
      - chat
      - edit
      - apply

context:
  - provider: file
  - provider: code
  - provider: codebase

rules:
  - Always use TypeScript interfaces
  - Give concise responses
```

#### 2. JSON Config (Deprecado)
**Ubicación**: `~/.continue/config.json`

#### 3. TypeScript Config (Avanzado)
**Ubicación**: `~/.continue/config.ts`
Para configuración programática avanzada.

### Configuración por Workspace
**Archivo**: `.continuerc.json` en la raíz del proyecto
- Permite configuración específica por proyecto
- Se fusiona con la configuración global

## Componentes de Configuración

### 1. Modelos (Models)

#### Propiedades Principales
```yaml
models:
  - name: "Nombre único"
    provider: "openai|anthropic|ollama|mistral|etc"
    model: "gpt-4|claude-3.5-sonnet|etc"
    apiKey: "tu-api-key"
    apiBase: "url-personalizada"
    roles: ["chat", "autocomplete", "edit", "apply", "summarize"]
    capabilities: ["tool_use", "image_input"]
```

#### Opciones de Completado
```yaml
defaultCompletionOptions:
  contextLength: 4096
  maxTokens: 1500
  temperature: 0.7
  topP: 0.9
  topK: 50
  stop: ["</code>", "\n\n"]
```

#### Proveedores Soportados
- **OpenAI**: GPT-4, GPT-3.5, etc.
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Haiku
- **Ollama**: Modelos locales (Llama, CodeLlama, etc.)
- **Mistral**: Mistral 7B, Codestral
- **Google**: Gemini Pro, Gemini Flash
- **Azure**: Azure OpenAI Service
- **Amazon Bedrock**: Claude, Llama en AWS
- **xAI**: Grok
- **DeepSeek**: DeepSeek Coder

### 2. Proveedores de Contexto (Context Providers)

#### Proveedores Integrados
```yaml
context:
  - provider: file          # Archivos del workspace
  - provider: code          # Funciones y clases específicas
  - provider: codebase      # Búsqueda semántica en el código
  - provider: diff          # Cambios de Git
  - provider: terminal      # Salida del terminal
  - provider: docs          # Documentación indexada
  - provider: web           # Búsqueda web
  - provider: search        # Búsqueda en código (ripgrep)
  - provider: url           # Contenido de URLs
  - provider: clipboard     # Historial del portapapeles
  - provider: tree          # Estructura del proyecto
  - provider: problems      # Errores y warnings del IDE
```

#### Proveedores Externos
```yaml
context:
  - provider: issue         # Issues de GitHub
    params:
      repos:
        - owner: continuedev
          repo: continue
      githubToken: "ghp_xxx"
  
  - provider: jira          # Tickets de Jira
    params:
      domain: "company.atlassian.net"
      token: "ATATT..."
  
  - provider: database      # Esquemas de BD
    params:
      connections:
        - name: postgres
          connection_type: postgres
          connection:
            host: localhost
            database: mydb
```

### 3. Reglas (Rules)

#### Definición de Reglas
```yaml
rules:
  - "Always use TypeScript interfaces for object shapes"
  - name: "Python Type Hints"
    rule: "Always annotate Python functions with parameter and return types"
    globs: "**/*.py"
  - name: "Test Patterns"
    rule: "Use Jest describe/it pattern for tests"
    globs: ["**/*.test.ts", "**/*.spec.ts"]
```

### 4. Prompts Personalizados

```yaml
prompts:
  - name: check
    description: "Check for mistakes in my code"
    prompt: |
      Please read the highlighted code and check for any mistakes.
      Look for syntax errors, logic errors, and security vulnerabilities.
  
  - name: test
    description: "Generate unit tests"
    prompt: |
      Write comprehensive unit tests for this function using Jest.
      Cover all edge cases and include descriptive test names.
```

### 5. Documentación (Docs)

```yaml
docs:
  - name: "Continue Docs"
    startUrl: "https://docs.continue.dev/intro"
    maxDepth: 4
    favicon: "https://docs.continue.dev/favicon.ico"
  
  - name: "React Docs"
    startUrl: "https://react.dev"
    maxDepth: 3
```

### 6. Servidores MCP (Model Context Protocol)

```yaml
mcpServers:
  - name: "SQLite Server"
    command: "uvx"
    args:
      - "mcp-server-sqlite"
      - "--db-path"
      - "/path/to/database.db"
    env:
      DEBUG: "true"
    connectionTimeout: 5000
```

## Roles de Modelos

### Tipos de Roles
- **chat**: Conversaciones y consultas generales
- **autocomplete**: Sugerencias de código en línea
- **edit**: Modificación de código existente
- **apply**: Aplicación de cambios sugeridos
- **summarize**: Resumen de código y documentación
- **embed**: Generación de embeddings para búsqueda
- **rerank**: Reordenamiento de resultados de búsqueda

### Configuración por Rol
```yaml
models:
  - name: "Chat Model"
    provider: anthropic
    model: claude-3.5-sonnet
    roles: [chat, edit, apply]
    
  - name: "Autocomplete Model"
    provider: ollama
    model: codellama:7b
    roles: [autocomplete]
    
  - name: "Embedding Model"
    provider: openai
    model: text-embedding-3-small
    roles: [embed]
```

## Configuración Avanzada

### Variables de Entorno y Secretos
```yaml
models:
  - name: "Secure Model"
    provider: openai
    apiKey: ${{ secrets.OPENAI_API_KEY }}
    model: gpt-4
```

### Configuración de Red
```yaml
models:
  - name: "Proxy Model"
    provider: openai
    model: gpt-4
    requestOptions:
      timeout: 30000
      proxy: "http://proxy.company.com:8080"
      headers:
        Custom-Header: "value"
      verifySsl: false
```

### Plantillas de Prompt
```yaml
models:
  - name: "Custom Template"
    provider: ollama
    model: llama2
    promptTemplates:
      chat: "llama3"
      edit: "custom-edit-template"
```

## Gestión de Asistentes

### Asistentes del Hub
- Disponibles en [hub.continue.dev](https://hub.continue.dev)
- Se sincronizan automáticamente
- Pueden importar bloques reutilizables

### Asistentes Locales
- Definidos en `~/.continue/assistants/`
- Específicos por workspace en `.continue/assistants/`
- Nombre del archivo = nombre del asistente

### Importación de Bloques
```yaml
models:
  - uses: anthropic/claude-3.5-sonnet
    with:
      ANTHROPIC_API_KEY: ${{ secrets.API_KEY }}
    override:
      temperature: 0.8
```

## Configuración de Datos de Desarrollo

```yaml
data:
  - name: "Local Analytics"
    destination: "file:///path/to/data"
    schema: "0.2.0"
    level: "noCode"
    events:
      - "autocomplete"
      - "chatInteraction"
  
  - name: "Company Analytics"
    destination: "https://analytics.company.com/ingest"
    schema: "0.2.0"
    apiKey: "analytics-key"
```

## Mejores Prácticas

### 1. Seguridad
- Usar variables de entorno para API keys
- No hardcodear credenciales en configuración
- Configurar timeouts apropiados

### 2. Rendimiento
- Usar modelos específicos por tarea
- Configurar límites de contexto apropiados
- Optimizar proveedores de contexto

### 3. Organización
- Usar reglas específicas por tipo de archivo
- Organizar prompts por funcionalidad
- Mantener configuración modular con bloques

### 4. Colaboración
- Usar `.continuerc.json` para configuración de equipo
- Compartir bloques en el hub
- Documentar configuraciones personalizadas

## Migración y Compatibilidad

### Migración de JSON a YAML
Continue.dev proporciona herramientas de migración automática desde `config.json` a `config.yaml`.

### Versionado de Esquemas
- Esquema actual: `v1`
- Compatibilidad hacia atrás mantenida
- Migración gradual recomendada

## Recursos Adicionales

- **Documentación**: [docs.continue.dev](https://docs.continue.dev)
- **Hub de Bloques**: [hub.continue.dev](https://hub.continue.dev)
- **Repositorio**: [github.com/continuedev/continue](https://github.com/continuedev/continue)
- **Discord**: Comunidad activa para soporte
- **Changelog**: [changelog.continue.dev](https://changelog.continue.dev)
